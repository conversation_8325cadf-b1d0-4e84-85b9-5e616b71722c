local enemy = script.Parent
local Enemies = require(game:GetService("ServerScriptService").Main.Enemy)

-- Get the current map
local map = workspace.Map:FindFirstChildOfClass("Folder")

-- Define the death spawn data
local deathSpawnData = {
	name = "Spawn2",
	quantity = 1,
	modifiers = nil  -- Add any modifiers you want here, like {Fortified = true}
}

-- Enable death spawning for this enemy using the Enemy module
Enemies.EnableDeathSpawning(enemy, deathSpawnData, map)
