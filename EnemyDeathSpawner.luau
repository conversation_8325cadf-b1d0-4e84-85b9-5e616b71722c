local Enemies = require(game:GetService("ServerScriptService").Main.Enemy)

-- Get the enemy this script is attached to
local enemy = script.Parent

-- Define the death spawn data
local deathSpawnData = {
	name = "Spawn2",
	quantity = 1,
	modifiers = nil  -- Add any modifiers you want here, like {Fortified = true}
}

-- Enable death spawning for this enemy
Enemies.EnableDeathSpawning(enemy, deathSpawnData)
