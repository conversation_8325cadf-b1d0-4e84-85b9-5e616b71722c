local ServerStorage = game:GetService("ServerStorage")
local PhysicsService = game:GetService("PhysicsService")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local events = ReplicatedStorage:WaitForChild("Events")


local spawnRequestEvent = ReplicatedStorage:WaitForChild("Events"):WaitFor<PERSON>hild("RequestUnitSpawn")

local animateUnitEvent = events:WaitForChild("AnimateUnit")

local Unit = {}

local function SendCash(player, value, allowed)
	if allowed == nil then
		allowed = true
	end
	if allowed == true then
		for _, plr in pairs(game.Players:GetPlayers()) do
			if plr ~= player and plr:FindFirstChild("Gold") then
				plr.Gold.Value += (value / #game.Players:GetPlayers() / 2)
			end
		end
	end
	if player and player:FindFirstChild("Gold") then
		player.Gold.Value += value
		print(player, value)
	else
		warn("Gold not found for player:", player, "Name:", player and player.Name or "Unknown")
	end
end


function Unit.FindTarget(newUnit)
	local DanDistance = newUnit.Config:FindFirstChild("Range") and newUnit.Config.Range.Value or 15
	local t = nil
	for _, target in ipairs (workspace.Enemy:GetChildren()) do
		local distance = (target.HumanoidRootPart.Position - newUnit.HumanoidRootPart.Position).Magnitude
			if distance < DanDistance then
			newUnit.Humanoid.WalkSpeed = 0
			t = target
			DanDistance = distance
		end
	end
	return t
end

function Unit.Attack(newUnit, player)
	
local target = Unit.FindTarget(newUnit)
local config = newUnit.Config

if target and target:FindFirstChild("Humanoid") and target.Humanoid.Health > 0 then
		local targetCFrame = CFrame.lookAt(newUnit.Head.Position, Vector3.new(target.Head. Position.X, newUnit.Head. Position.Y, target. Head.Position.Z))
		
		newUnit.Head.CFrame = targetCFrame
		
		animateUnitEvent:FireAllClients (newUnit, "Attack", target)
		
		if not target.Config:FindFirstChild("DefensePercent") then
			if not target.Config:FindFirstChild("NoMoney") or target.Config.NoMoney.Value == false then
				if target.Humanoid.Health < config.Damage.Value then
					SendCash(player, target.Humanoid.Health)
					
				else
					SendCash(player, config.Damage.Value)
				end
			end
			target.Humanoid:TakeDamage(config.Damage.Value)

		else
			if not target.Config:FindFirstChild("NoMoney") or target.Config.NoMoney.Value == false then
				if target.Humanoid.Health < math.round(config.Damage.Value - (config.Damage.Value * (target.Config.DefensePercent.Value / 100))) then
					
					SendCash(player, target.Humanoid.Health)
				else
					SendCash(player, math.round(config.Damage.Value - (config.Damage.Value * (target.Config.DefensePercent.Value / 100))))
				end
			end
			target.Humanoid:TakeDamage(math.round(config.Damage.Value - (config.Damage.Value * (target.Config.DefensePercent.Value / 100))))

		end
		if newUnit.Head:FindFirstChild("Shoot") then
			newUnit.Head.Shoot:Play()
		end
		task.wait(config.Cooldown.Value)
	elseif not target or target.Humanoid.Health == 0 then
		newUnit.Humanoid.WalkSpeed = newUnit.Config.NormalSpeed.Value or 5
	end
	task.wait()
	if newUnit and newUnit.Parent then
		task.spawn(function()
		Unit.Attack(newUnit, player)
		end)
	end
end




function Unit.Optimize(UnitToOptimize)
	local humanoid = UnitToOptimize:FindFirstChild("Humanoid")

	if UnitToOptimize:FindFirstChild("HumanoidRootPart") then 
		UnitToOptimize.HumanoidRootPart:SetNetworkOwner(nil) 
	elseif UnitToOptimize.PrimaryPart ~= nil then
		UnitToOptimize.PrimaryPart:SetNetworkOwner(nil)
	end
	if not humanoid then return end
	humanoid:SetStateEnabled(Enum.HumanoidStateType.Seated, false)
	humanoid:SetStateEnabled(Enum.HumanoidStateType.Jumping, false)
	humanoid:SetStateEnabled(Enum.HumanoidStateType.Running, false)
	humanoid:SetStateEnabled(Enum.HumanoidStateType.GettingUp, false)
	humanoid:SetStateEnabled(Enum.HumanoidStateType.Climbing, false)
	humanoid:SetStateEnabled(Enum.HumanoidStateType.Landed, false)
	humanoid:SetStateEnabled(Enum.HumanoidStateType.Ragdoll, false)
	humanoid:SetStateEnabled(Enum.HumanoidStateType.Freefall, false)
end

function Unit.Move(newUnit, map)
	task.defer(function()
		-- Initial check
		if not newUnit or not map then
			warn("Move function aborted: newUnit or map is nil.")
			return
		end

		local humanoid = newUnit:FindFirstChild("Humanoid")

		if not humanoid then
			warn("Humanoid not found in unit:", newUnit)
			return
		end

		local waypoints = map:FindFirstChild("Waypoints")



		local offset = Vector3.new(math.random(-50, 50) / 100, 0, math.random(-50, 50) / 100)


		for waypoint = #waypoints:GetChildren(), 1, -1 do
			if not newUnit.Parent then
				return
			end

			newUnit.MovingTo.Value = waypoint

			local targetPosition = waypoints[waypoint].Position + offset
			repeat
				humanoid:MoveTo(targetPosition)
			until humanoid.MoveToFinished:Wait() or not newUnit.Parent
		end

		if newUnit and newUnit.Parent then
			newUnit:Destroy()
		end
	end)
end



function Unit.Spawn(name, quantity, map, unitOwnerValue)
	
	local UnitExists = ServerStorage.Units:FindFirstChild(name)
	local SpawnedUnits = {}
	if UnitExists then
		for i = 1, quantity do
			task.wait(0.5)
			local RegisteredMap = map
			local newUnit = UnitExists:Clone()
			newUnit.HumanoidRootPart.CFrame = RegisteredMap.Base.Head.CFrame
			newUnit.Parent = workspace.Unit
			Unit.Optimize(newUnit)

			local movingTo = Instance.new("IntValue")
			movingTo.Name = "MovingTo"
			movingTo.Value = #RegisteredMap.Waypoints:GetChildren()
			movingTo.Parent = newUnit

			local NormalSpeed = newUnit.Config:FindFirstChild("NormalSpeed") or Instance.new("NumberValue")
			NormalSpeed.Name = "NormalSpeed"
			NormalSpeed.Value = newUnit.Humanoid.WalkSpeed
			NormalSpeed.Parent = newUnit.Config

			-- Set the unit owner value
			local unitOwner = newUnit:FindFirstChild("Config"):FindFirstChild("UnitOwner")
			if unitOwner then
				unitOwner.Value = unitOwnerValue
			end

			for _, object in ipairs(newUnit:GetDescendants()) do
				if object:IsA("BasePart") then
					object.CollisionGroup = "Unit"
				end
			end

			newUnit.HumanoidRootPart.Touched:Connect(function(hit)
				if not hit.Parent then return end

				local enemy = hit.Parent
				if enemy.Parent == workspace.Enemy and enemy:FindFirstChild("Humanoid") then
					local enemyHumanoid = enemy:FindFirstChild("Humanoid")
					local unitHealth = newUnit.Humanoid.Health
					local enemyHealth = enemyHumanoid.Health
					local damageDealt
					local unitOwner = newUnit:FindFirstChild("Config"):FindFirstChild("UnitOwner")
					local player = game.Players:FindFirstChild(unitOwner.Value)
					
					if enemyHealth > unitHealth then
						newUnit.Humanoid.Health = 0
						damageDealt = unitHealth
						if unitOwner and unitOwner.Value then
							if not enemy.Config:FindFirstChild("NoMoney") or enemy.Config.NoMoney.Value == false then
							SendCash(player,damageDealt)
							end
						end
						enemy.Humanoid.Health -= unitHealth
					elseif unitHealth > enemyHealth then
						enemy.Humanoid.Health = 0
						damageDealt = enemyHealth
						if unitOwner and unitOwner.Value then
							if not enemy.Config:FindFirstChild("NoMoney") or enemy.Config.NoMoney.Value == false then
								SendCash(player,damageDealt)
							end
						end
						newUnit.Humanoid.Health -= enemyHealth
					else
						damageDealt = unitHealth
						if unitOwner and unitOwner.Value then
							if not enemy.Config:FindFirstChild("NoMoney") or enemy.Config.NoMoney.Value == false then
								SendCash(player,damageDealt)
							end
						end
						newUnit.Humanoid.Health = 0
						enemy.Humanoid.Health = 0
					end
				end
			end)

			newUnit.Humanoid.Died:Connect(function()
				task.wait(0.5)
				newUnit:Destroy()
			end)

			coroutine.wrap(Unit.Move)(newUnit, RegisteredMap)
			coroutine.wrap(Unit.Attack)(newUnit, game.Players:FindFirstChild(unitOwnerValue))
			table.insert(SpawnedUnits, newUnit)
		end
	else
		warn("Requested Unit does not exist:", name)
	end
	
	return SpawnedUnits

end

function Unit.Summon(name, quantity, map, cframe, MovingToVal, unitOwnerValue)
	local UnitExists = ServerStorage.Units:FindFirstChild(name)
	local SpawnedUnits = {}
	if UnitExists then
		for i = 1, quantity do
			task.wait(0.5)

			local newUnit = UnitExists:Clone()
			newUnit.HumanoidRootPart.CFrame = cframe
			newUnit.Parent = workspace.Unit
			newUnit.HumanoidRootPart:SetNetworkOwner(nil)

			local movingTo = Instance.new("IntValue")
			movingTo.Name = "MovingTo"
			movingTo.Value = MovingToVal or #map.Waypoints:GetChildren()
			movingTo.Parent = newUnit

			local NormalSpeed = newUnit.Config:FindFirstChild("NormalSpeed") or Instance.new("NumberValue")
			NormalSpeed.Name = "NormalSpeed"
			NormalSpeed.Value = newUnit.Humanoid.WalkSpeed
			NormalSpeed.Parent = newUnit.Config

			-- Set the unit owner value
			local unitOwner = newUnit:FindFirstChild("Config"):FindFirstChild("UnitOwner")
			if unitOwner then
				unitOwner.Value = unitOwnerValue
			end

			for _, object in ipairs(newUnit:GetDescendants()) do
				if object:IsA("BasePart") then
					object.CollisionGroup = "Unit"
				end
			end

			newUnit.HumanoidRootPart.Touched:Connect(function(hit)
				if not hit.Parent then return end

				local enemy = hit.Parent					
				local unitOwner = newUnit:FindFirstChild("Config"):FindFirstChild("UnitOwner")
				local player = game.Players:FindFirstChild(unitOwner.Value)
				if enemy.Parent == workspace.Enemy and enemy:FindFirstChild("Humanoid") then
					local enemyHumanoid = enemy:FindFirstChild("Humanoid")
					local unitHealth = newUnit.Humanoid.Health
					local enemyHealth = enemyHumanoid.Health
					local damageDealt

					if enemyHealth > unitHealth then
						newUnit.Humanoid.Health = 0
						damageDealt = unitHealth
						if not enemy.Config:FindFirstChild("NoMoney") or enemy.Config.NoMoney.Value == false then
							SendCash(player,damageDealt)
						end
						enemy.Humanoid.Health -= unitHealth
					elseif unitHealth > enemyHealth then
						enemy.Humanoid.Health = 0
						damageDealt = enemyHealth
						if not enemy.Config:FindFirstChild("NoMoney") or enemy.Config.NoMoney.Value == false then
							SendCash(player,damageDealt)
						end
						newUnit.Humanoid.Health -= enemyHealth
					else
						damageDealt = unitHealth
						if not enemy.Config:FindFirstChild("NoMoney") or enemy.Config.NoMoney.Value == false then
							SendCash(player,damageDealt)
						end
						newUnit.Humanoid.Health = 0
						enemy.Humanoid.Health = 0
					end

				end
			end)

			newUnit.Humanoid.Died:Connect(function()
				task.wait(0.5)
				newUnit:Destroy()
			end)

			coroutine.wrap(Unit.Move)(newUnit, map)
			coroutine.wrap(Unit.Attack)(newUnit, game.Players:FindFirstChild(unitOwnerValue))
			table.insert(SpawnedUnits, newUnit)
		end
	else
		warn("Requested Unit does not exist:", name)
	end
	return SpawnedUnits
end
-- Function to handle BindableEvent spawn requests
local function onRequestSpawn(name, quantity, map, unitOwnerValue)
	Unit.Spawn(name, quantity, map, unitOwnerValue)
end

-- Connect the BindableEvent to the handler
spawnRequestEvent.Event:Connect(onRequestSpawn)


return Unit
