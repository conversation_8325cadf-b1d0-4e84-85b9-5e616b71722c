local ServerStorage = game:GetService("ServerStorage")
local PhysicsService = game:GetService("PhysicsService")
local ContentProvider = game:GetService("ContentProvider")

local mob = {}

-- Function to adjust stats for Fortified and Fleetfooted
local function adjustStats(newEnemy, modifier, enabled)
	if modifier == "Fortified" then
		local humanoid = newEnemy:FindFirstChild("Humanoid")
		if humanoid and enabled == true then
			humanoid.MaxHealth *= 2
			humanoid.Health *= 2
		end
	elseif modifier == "Fleetfooted" then
		local humanoid = newEnemy:FindFirstChild("Humanoid")
		local normalSpeed = newEnemy.Config:FindFirstChild("NormalSpeed")
		if humanoid and normalSpeed  and enabled == true then
			humanoid.WalkSpeed *= 2
			normalSpeed.Value *= 2
			if newEnemy:FindFirstChild("Animations") and newEnemy:FindFirstChild("Animations"):FindFirstChild("WalkAnim") then
				local animspeed = newEnemy.Animations.WalkAnim:FindFirstChild("AnimSpeed") or Instance.new("NumberValue")
				animspeed.Name = "AnimSpeed"
				animspeed.Parent = newEnemy.Animations.WalkAnim
				animspeed.Value = 2
			end
		end
	end
end

-- Function to track modifiers dynamically
local function trackModifiers(newEnemy)
	local config = newEnemy:FindFirstChild("Config")
	if not config then return end

	for _, child in ipairs(config:GetChildren()) do
		if child:IsA("BoolValue") then
			local modifier = child.Name

			-- Apply the initial state of the modifier
			adjustStats(newEnemy, modifier, child.Value)

			-- Listen for changes and adjust dynamically
			child.Changed:Connect(function(newValue)
				adjustStats(newEnemy, modifier, newValue)
			end)
		end
	end
end

-- Function to modify enemy configuration
local function ModifyConfiguration(enemy1, modifiers)
	if not enemy1 or not enemy1:FindFirstChild("Config") then
		warn("Enemy or enemy configuration is missing.")
		return
	end

	for modifier, value in pairs(modifiers) do
		local configModifier = enemy1.Config:FindFirstChild(modifier)

		if configModifier then
			-- If the modifier already exists, update its value
			configModifier.Value = value
		else
			-- Create the modifier based on its type
			local newModifier
			if typeof(value) == "boolean" then
				newModifier = Instance.new("BoolValue")
			elseif typeof(value) == "number" then
				newModifier = Instance.new("NumberValue")
			else
				warn("Unsupported modifier type for:", modifier)
				newModifier = nil
			end

			if newModifier then
				newModifier.Name = modifier
				newModifier.Value = value
				newModifier.Parent = enemy1.Config
			end
		end
	end
end

-- Function to summon enemy on death
local function SummonEnemyOnDeath(name, quantity, map, cframe, movingToVal, modifiers)
	for i = 1, quantity do
		-- Create the enemy manually to avoid recursion and ensure proper setup
		local mobExists = ServerStorage.Enemies:FindFirstChild(name)
		if not mobExists then
			warn("Requested mob does not exist:", name)
			return
		end

		local newMob = mobExists:Clone()
		newMob.HumanoidRootPart.CFrame = cframe
		newMob.Parent = workspace.Enemy
		newMob.HumanoidRootPart:SetNetworkOwner(nil)
		mob.Optimize(newMob)

		-- Set up MovingTo value
		local movingTo = Instance.new("IntValue")
		movingTo.Name = "MovingTo"
		movingTo.Value = movingToVal
		movingTo.Parent = newMob

		-- Set up NormalSpeed
		local NormalSpeed = newMob.Config:FindFirstChild("NormalSpeed") or Instance.new("NumberValue")
		NormalSpeed.Name = "NormalSpeed"
		NormalSpeed.Value = newMob.Humanoid.WalkSpeed
		NormalSpeed.Parent = newMob.Config

		-- Set NoMoney flag
		local NoMoney = newMob.Config:FindFirstChild("NoMoney") or Instance.new("BoolValue")
		NoMoney.Value = true
		NoMoney.Name = "NoMoney"
		NoMoney.Parent = newMob.Config

		-- Handle spawned enemy setup
		task.spawn(function()
			mob.HandleSpawnedEnemy(newMob)
		end)

		-- Set collision group
		for _, object in ipairs(newMob:GetDescendants()) do
			if object:IsA("BasePart") then
				object.CollisionGroup = "Enemy"
			end
		end

		-- Apply modifiers if provided
		if modifiers then
			ModifyConfiguration(newMob, modifiers)
		end

		-- Set up death handler (without recursive spawning)
		newMob.Humanoid.Died:Connect(function()
			local DeathSound = newMob.Head:FindFirstChild("DeathSound")
			if DeathSound then
				DeathSound:Play()
			end
			local hrp = newMob:FindFirstChild("HumanoidRootPart") or newMob.PrimaryPart
			hrp.Anchored = true
			if newMob:FindFirstChild("Animations") and newMob.Animations:FindFirstChild("DeathAnim") then
				local deathAnim = newMob.Animations.DeathAnim
				ContentProvider:PreloadAsync({deathAnim})

				local animationTrack = newMob.Humanoid:LoadAnimation(deathAnim)
				animationTrack:Play()

				task.spawn(function()
					task.wait(animationTrack.Length - 0.1)
					newMob:Destroy()
				end)
			else
				task.wait(0.5)
				newMob:Destroy()
			end
		end)

		-- Start movement - THIS IS THE KEY PART THAT WAS MISSING!
		coroutine.wrap(mob.Move)(newMob, map, movingTo)
	end
end

-- Function to enable death spawning on an enemy
function mob.EnableDeathSpawning(enemy, spawnData)
	if not enemy or not enemy:FindFirstChild("Humanoid") then
		warn("Invalid enemy provided to EnableDeathSpawning")
		return
	end

	enemy.Humanoid.Died:Once(function()
		task.spawn(function()
			local map = workspace.Map:FindFirstChildOfClass("Folder")
			local cframe = enemy.PrimaryPart.CFrame
			local movingToVal = enemy.MovingTo.Value

			SummonEnemyOnDeath(
				spawnData.name or "Spawn2",
				spawnData.quantity or 1,
				map,
				cframe,
				movingToVal,
				spawnData.modifiers
			)
		end)
	end)
end

-- Function to handle a spawned enemy
function mob.HandleSpawnedEnemy(newEnemy)
	-- Ensure the enemy has a Config folder
	local config = newEnemy:FindFirstChild("Config")
	if not config then
		config = Instance.new("Folder")
		config.Name = "Config"
		config.Parent = newEnemy
	end

	-- Add placeholders for modifiers if they don't exist
	if not config:FindFirstChild("Fortified") then
		local fortified = Instance.new("BoolValue")
		fortified.Name = "Fortified"
		fortified.Value = false
		fortified.Parent = config
	end

	if not config:FindFirstChild("Fleetfooted") then
		local fleetfooted = Instance.new("BoolValue")
		fleetfooted.Name = "Fleetfooted"
		fleetfooted.Value = false
		fleetfooted.Parent = config
	end

	-- Start tracking modifiers
	trackModifiers(newEnemy)
end


function mob.Optimize(mobToOptimize)
	local humanoid = mobToOptimize:FindFirstChild("Humanoid")

	if mobToOptimize:FindFirstChild("HumanoidRootPart") then 
		mobToOptimize.HumanoidRootPart:SetNetworkOwner(nil) 
	elseif mobToOptimize.PrimaryPart ~= nil then
		mobToOptimize.PrimaryPart:SetNetworkOwner(nil)
	end
	if not humanoid then return end
	humanoid:SetStateEnabled(Enum.HumanoidStateType.Seated, false)
	humanoid:SetStateEnabled(Enum.HumanoidStateType.Jumping, false)
	humanoid:SetStateEnabled(Enum.HumanoidStateType.Running, false)
	humanoid:SetStateEnabled(Enum.HumanoidStateType.GettingUp, false)
	humanoid:SetStateEnabled(Enum.HumanoidStateType.Climbing, false)
	humanoid:SetStateEnabled(Enum.HumanoidStateType.Landed, false)
	humanoid:SetStateEnabled(Enum.HumanoidStateType.Ragdoll, false)
	humanoid:SetStateEnabled(Enum.HumanoidStateType.Freefall, false)
end
function mob.Move(mob, map, movingToVal)
	local humanoid = mob:WaitForChild("Humanoid")
	local waypoints = map:FindFirstChild("Waypoints")
	if not waypoints then
		warn("Waypoints not found in map:", map)
		return
	end

	local offset = Vector3.new(math.random(-50, 50) / 100, 0, math.random(-50, 50) / 100)

	if movingToVal == nil then
		for waypoint = 1, #waypoints:GetChildren() do
			mob.MovingTo.Value = waypoint
			local targetPosition = waypoints[waypoint].Position + offset

			repeat
				humanoid:MoveTo(targetPosition)
			until humanoid.MoveToFinished:Wait() or not mob.Parent
		end

		mob:Destroy()
		map.Base.Humanoid:TakeDamage(humanoid.Health)
	else
		for waypoint = movingToVal.Value, #waypoints:GetChildren() do
			mob.MovingTo.Value = waypoint
			local targetPosition = waypoints[waypoint].Position + offset

			repeat
				humanoid:MoveTo(targetPosition)
			until humanoid.MoveToFinished:Wait() or not mob.Parent
		end

		mob:Destroy()
		map.Base.Humanoid:TakeDamage(humanoid.Health)
	end
end


function mob.Spawn(name, quantity, map)
	local mobExists = ServerStorage.Enemies:FindFirstChild(name)
	local SpawnedEnemies = {}
	if mobExists then
		for i = 1, quantity do
			task.wait(0.5)

			local newMob = mobExists:Clone()
			newMob.HumanoidRootPart.CFrame = map.Start.CFrame
			newMob.Parent = workspace.Enemy
			mob.Optimize(newMob)


			local movingTo = Instance.new("IntValue")
			movingTo.Name = "MovingTo"
			movingTo.Value = 1
			movingTo.Parent = newMob


			local NormalSpeed = newMob.Config:FindFirstChild("NormalSpeed") or Instance.new("NumberValue")
			NormalSpeed.Name = "NormalSpeed"
			NormalSpeed.Value = newMob.Humanoid.WalkSpeed
			NormalSpeed.Parent = newMob.Config

			task.spawn(function()
				mob.HandleSpawnedEnemy(newMob)
			end)

			for _, object in ipairs(newMob:GetDescendants()) do
				if object:IsA("BasePart") then
					object.CollisionGroup = "Enemy"
				end
			end



			newMob.Humanoid.Died:Connect(function()
				local DeathSound = newMob.Head:FindFirstChild("DeathSound")
				if DeathSound then
					DeathSound:Play()
				end
				if newMob:FindFirstChild("Animations") and newMob.Animations:FindFirstChild("DeathAnim") then
					local deathAnim = newMob.Animations.DeathAnim
					ContentProvider:PreloadAsync({deathAnim})
					local hrp = newMob:FindFirstChild("HumanoidRootPart") or newMob.PrimaryPart
					hrp.Anchored = true
					local animationTrack = newMob.Humanoid:LoadAnimation(deathAnim)
					animationTrack:Play()

					task.spawn(function()
						task.wait(animationTrack.Length - 0.1)
						newMob:Destroy()
					end)
				else
					task.wait(0.5)
					newMob:Destroy()
				end
			end)


			coroutine.wrap(mob.Move)(newMob, map, movingTo)
			table.insert(SpawnedEnemies, newMob)
		end
	else
		warn("Requested mob does not exist:", name)
	end
	return SpawnedEnemies
end
function mob.Summon(name, quantity, map, cframe, MovingToVal, deathSpawnData)
	local mobExists = ServerStorage.Enemies:FindFirstChild(name)
	local SpawnedEnemies = {}
	if mobExists then
		for i = 1, quantity do
			task.wait(0.5)

			local newMob = mobExists:Clone()
			newMob.HumanoidRootPart.CFrame = cframe
			newMob.Parent = workspace.Enemy
			newMob.HumanoidRootPart:SetNetworkOwner(nil)
			mob.Optimize(newMob)


			local movingTo = Instance.new("IntValue")
			movingTo.Name = "MovingTo"
			movingTo.Value = MovingToVal
			movingTo.Parent = newMob


			local NormalSpeed = newMob.Config:FindFirstChild("NormalSpeed") or Instance.new("NumberValue")
			NormalSpeed.Name = "NormalSpeed"
			NormalSpeed.Value = newMob.Humanoid.WalkSpeed
			NormalSpeed.Parent = newMob.Config

			local NoMoney = newMob.Config:FindFirstChild("NoMoney") or Instance.new("BoolValue")
			NoMoney.Value = true
			NoMoney.Name = "NoMoney"
			NoMoney.Parent = newMob.Config

			task.spawn(function()
				mob.HandleSpawnedEnemy(newMob)
			end)

			for _, object in ipairs(newMob:GetDescendants()) do
				if object:IsA("BasePart") then
					object.CollisionGroup = "Enemy"
				end
			end


			newMob.Humanoid.Died:Connect(function()
				-- Handle death spawning if data is provided
				if deathSpawnData then
					task.spawn(function()
						local currentMap = workspace.Map:FindFirstChildOfClass("Folder")
						local currentCFrame = newMob.PrimaryPart.CFrame
						local currentMovingTo = newMob.MovingTo.Value
						SummonEnemyOnDeath(
							deathSpawnData.name or "Spawn2",
							deathSpawnData.quantity or 1,
							currentMap,
							currentCFrame,
							currentMovingTo,
							deathSpawnData.modifiers
						)
					end)
				end

				local DeathSound = newMob.Head:FindFirstChild("DeathSound")
				if DeathSound then
					DeathSound:Play()
				end
				local hrp = newMob:FindFirstChild("HumanoidRootPart") or newMob.PrimaryPart
				hrp.Anchored = true
				if newMob:FindFirstChild("Animations") and newMob.Animations:FindFirstChild("DeathAnim") then
					local deathAnim = newMob.Animations.DeathAnim
					ContentProvider:PreloadAsync({deathAnim})

					local animationTrack = newMob.Humanoid:LoadAnimation(deathAnim)
					animationTrack:Play()

					task.spawn(function()
						task.wait(animationTrack.Length - 0.1)
						newMob:Destroy()
					end)
				else
					task.wait(0.5)
					newMob:Destroy()
				end
			end)



			coroutine.wrap(mob.Move)(newMob, map, movingTo)
			table.insert(SpawnedEnemies, newMob)
		end
	else
		warn("Requested mob does not exist:", name)
	end
	return SpawnedEnemies
end

return mob
