local ServerStorage = game:GetService("ServerStorage")
local PhysicsService = game:GetService("PhysicsService")
local ContentProvider = game:GetService("ContentProvider")

local mob = {}

-- Store death spawn configurations for enemies
local deathSpawnConfigs = {}

-- Function to adjust stats for Fortified and Fleetfooted
local function adjustStats(newEnemy, modifier, enabled)
	if modifier == "Fortified" then
		local humanoid = newEnemy:FindFirstChild("Humanoid")
		if humanoid and enabled == true then
			humanoid.MaxHealth = humanoid.MaxHealth * 2
			humanoid.Health = humanoid.Health * 2
		end
	elseif modifier == "Fleetfooted" then
		local humanoid = newEnemy:FindFirstChild("Humanoid")
		local normalSpeed = newEnemy.Config:FindFirstChild("NormalSpeed")
		if humanoid and normalSpeed and enabled == true then
			humanoid.WalkSpeed = humanoid.WalkSpeed * 2
			normalSpeed.Value = normalSpeed.Value * 2
			if newEnemy:Find<PERSON>irs<PERSON><PERSON>hild("Animations") and newEnemy:<PERSON><PERSON>irs<PERSON><PERSON>hild("Animations"):Find<PERSON>irs<PERSON><PERSON>hild("WalkAnim") then
				local animspeed = newEnemy.Animations.WalkAnim:FindFirstChild("AnimSpeed") or Instance.new("NumberValue")
				animspeed.Name = "AnimSpeed"
				animspeed.Parent = newEnemy.Animations.WalkAnim
				animspeed.Value = 2
			end
		end
	end
end

-- Function to track modifiers dynamically
local function trackModifiers(newEnemy)
	local config = newEnemy:FindFirstChild("Config")
	if not config then return end

	for _, child in ipairs(config:GetChildren()) do
		if child:IsA("BoolValue") then
			local modifier = child.Name
			-- Apply the initial state of the modifier
			adjustStats(newEnemy, modifier, child.Value)
			-- Listen for changes and adjust dynamically
			child.Changed:Connect(function(newValue)
				adjustStats(newEnemy, modifier, newValue)
			end)
		end
	end
end

-- Function to modify enemy configuration
local function modifyConfiguration(enemy, modifiers)
	if not enemy or not enemy:FindFirstChild("Config") then
		warn("Enemy or enemy configuration is missing.")
		return
	end

	for modifier, value in pairs(modifiers) do
		local configModifier = enemy.Config:FindFirstChild(modifier)

		if configModifier then
			configModifier.Value = value
		else
			local newModifier
			if typeof(value) == "boolean" then
				newModifier = Instance.new("BoolValue")
			elseif typeof(value) == "number" then
				newModifier = Instance.new("NumberValue")
			else
				warn("Unsupported modifier type for:", modifier)
				continue
			end

			if newModifier then
				newModifier.Name = modifier
				newModifier.Value = value
				newModifier.Parent = enemy.Config
			end
		end
	end
end

-- Function to handle a spawned enemy
function mob.HandleSpawnedEnemy(newEnemy)
	-- Ensure the enemy has a Config folder
	local config = newEnemy:FindFirstChild("Config")
	if not config then
		config = Instance.new("Folder")
		config.Name = "Config"
		config.Parent = newEnemy
	end

	-- Add placeholders for modifiers if they don't exist
	if not config:FindFirstChild("Fortified") then
		local fortified = Instance.new("BoolValue")
		fortified.Name = "Fortified"
		fortified.Value = false
		fortified.Parent = config
	end

	if not config:FindFirstChild("Fleetfooted") then
		local fleetfooted = Instance.new("BoolValue")
		fleetfooted.Name = "Fleetfooted"
		fleetfooted.Value = false
		fleetfooted.Parent = config
	end

	-- Start tracking modifiers
	trackModifiers(newEnemy)
end

-- Function to optimize enemy for performance
function mob.Optimize(mobToOptimize)
	local humanoid = mobToOptimize:FindFirstChild("Humanoid")

	if mobToOptimize:FindFirstChild("HumanoidRootPart") then
		mobToOptimize.HumanoidRootPart:SetNetworkOwner(nil)
	elseif mobToOptimize.PrimaryPart ~= nil then
		mobToOptimize.PrimaryPart:SetNetworkOwner(nil)
	end

	if not humanoid then return end

	humanoid:SetStateEnabled(Enum.HumanoidStateType.Seated, false)
	humanoid:SetStateEnabled(Enum.HumanoidStateType.Jumping, false)
	humanoid:SetStateEnabled(Enum.HumanoidStateType.Running, false)
	humanoid:SetStateEnabled(Enum.HumanoidStateType.GettingUp, false)
	humanoid:SetStateEnabled(Enum.HumanoidStateType.Climbing, false)
	humanoid:SetStateEnabled(Enum.HumanoidStateType.Landed, false)
	humanoid:SetStateEnabled(Enum.HumanoidStateType.Ragdoll, false)
	humanoid:SetStateEnabled(Enum.HumanoidStateType.Freefall, false)
end

-- Function to enable death spawning on an enemy
function mob.EnableDeathSpawning(enemy, spawnData)
	if not enemy or not enemy:FindFirstChild("Humanoid") then
		warn("Invalid enemy provided to EnableDeathSpawning")
		return
	end

	-- Store the death spawn configuration
	deathSpawnConfigs[enemy] = spawnData
end

-- Function to handle enemy death and spawning
local function handleEnemyDeath(enemy)
	-- Check if this enemy has death spawn configuration
	local spawnData = deathSpawnConfigs[enemy]
	if spawnData then
		task.spawn(function()
			local map = workspace.Map:FindFirstChildOfClass("Folder")
			if map then
				local cframe = enemy.PrimaryPart.CFrame
				local movingToVal = enemy.MovingTo.Value

				-- Spawn the new enemies
				for i = 1, spawnData.quantity or 1 do
					local newEnemies = mob.Summon(
						spawnData.name or "Spawn2",
						1,
						map,
						cframe,
						movingToVal
					)

					-- Apply modifiers if provided
					if newEnemies and newEnemies[1] and spawnData.modifiers then
						modifyConfiguration(newEnemies[1], spawnData.modifiers)
					end
				end
			end
		end)

		-- Clean up the death spawn configuration
		deathSpawnConfigs[enemy] = nil
	end
end

-- Function to move enemy through waypoints
function mob.Move(enemy, map, movingToVal)
	local humanoid = enemy:WaitForChild("Humanoid")
	local waypoints = map:FindFirstChild("Waypoints")

	if not waypoints then
		warn("Waypoints not found in map:", map.Name or "Unknown")
		return
	end

	local offset = Vector3.new(math.random(-50, 50) / 100, 0, math.random(-50, 50) / 100)
	local startWaypoint = movingToVal and movingToVal.Value or 1
	local totalWaypoints = #waypoints:GetChildren()

	for waypoint = startWaypoint, totalWaypoints do
		if not enemy.Parent then
			return
		end

		enemy.MovingTo.Value = waypoint
		local targetWaypoint = waypoints:FindFirstChild(tostring(waypoint))

		if targetWaypoint then
			local targetPosition = targetWaypoint.Position + offset

			repeat
				humanoid:MoveTo(targetPosition)
			until humanoid.MoveToFinished:Wait() or not enemy.Parent
		else
			warn("Waypoint", waypoint, "not found in map")
		end
	end

	-- Enemy reached the end, damage base and destroy
	if enemy.Parent then
		enemy:Destroy()
		if map.Base and map.Base:FindFirstChild("Humanoid") then
			map.Base.Humanoid:TakeDamage(humanoid.Health)
		end
	end
end
-- Function to create and setup a new enemy
local function createEnemy(name, map, cframe, movingToVal, isSpawned)
	local mobExists = ServerStorage.Enemies:FindFirstChild(name)
	if not mobExists then
		warn("Requested mob does not exist:", name)
		return nil
	end

	local newMob = mobExists:Clone()
	newMob.HumanoidRootPart.CFrame = cframe
	newMob.Parent = workspace.Enemy
	newMob.HumanoidRootPart:SetNetworkOwner(nil)
	mob.Optimize(newMob)

	-- Set up MovingTo value
	local movingTo = Instance.new("IntValue")
	movingTo.Name = "MovingTo"
	movingTo.Value = movingToVal or 1
	movingTo.Parent = newMob

	-- Set up NormalSpeed
	local NormalSpeed = newMob.Config:FindFirstChild("NormalSpeed") or Instance.new("NumberValue")
	NormalSpeed.Name = "NormalSpeed"
	NormalSpeed.Value = newMob.Humanoid.WalkSpeed
	NormalSpeed.Parent = newMob.Config

	-- Set NoMoney flag for summoned enemies
	if not isSpawned then
		local NoMoney = newMob.Config:FindFirstChild("NoMoney") or Instance.new("BoolValue")
		NoMoney.Value = true
		NoMoney.Name = "NoMoney"
		NoMoney.Parent = newMob.Config
	end

	-- Handle spawned enemy setup
	task.spawn(function()
		mob.HandleSpawnedEnemy(newMob)
	end)

	-- Set collision group
	for _, object in ipairs(newMob:GetDescendants()) do
		if object:IsA("BasePart") then
			object.CollisionGroup = "Enemy"
		end
	end

	-- Set up death handler
	newMob.Humanoid.Died:Connect(function()
		-- Handle death spawning first
		handleEnemyDeath(newMob)

		-- Play death sound
		local DeathSound = newMob.Head:FindFirstChild("DeathSound")
		if DeathSound then
			DeathSound:Play()
		end

		-- Anchor the enemy
		local hrp = newMob:FindFirstChild("HumanoidRootPart") or newMob.PrimaryPart
		if hrp then
			hrp.Anchored = true
		end

		-- Play death animation if available
		if newMob:FindFirstChild("Animations") and newMob.Animations:FindFirstChild("DeathAnim") then
			local deathAnim = newMob.Animations.DeathAnim
			ContentProvider:PreloadAsync({deathAnim})

			local animationTrack = newMob.Humanoid:LoadAnimation(deathAnim)
			animationTrack:Play()

			task.spawn(function()
				task.wait(animationTrack.Length - 0.1)
				newMob:Destroy()
			end)
		else
			task.wait(0.5)
			newMob:Destroy()
		end
	end)

	-- Start movement
	coroutine.wrap(mob.Move)(newMob, map, movingTo)

	return newMob
end

-- Function to spawn enemies from the start
function mob.Spawn(name, quantity, map)
	local SpawnedEnemies = {}

	for i = 1, quantity do
		task.wait(0.5)

		local newMob = createEnemy(name, map, map.Start.CFrame, 1, true)
		if newMob then
			table.insert(SpawnedEnemies, newMob)
		end
	end

	return SpawnedEnemies
end

-- Function to summon enemies at specific location and waypoint
function mob.Summon(name, quantity, map, cframe, MovingToVal)
	local SpawnedEnemies = {}

	for i = 1, quantity do
		task.wait(0.5)

		local newMob = createEnemy(name, map, cframe, MovingToVal, false)
		if newMob then
			table.insert(SpawnedEnemies, newMob)
		end
	end

	return SpawnedEnemies
end

return mob
