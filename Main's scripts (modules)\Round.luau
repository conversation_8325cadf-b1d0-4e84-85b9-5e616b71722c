local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local ServerStorage = game:GetService("ServerStorage")
local Lighting = game:GetService("Lighting")
local events = ReplicatedStorage:WaitForChild("Events")
local musics = ReplicatedStorage:WaitForChild("Music")
local CurrentMusic
local mob = require(script.Parent.Enemy)
local info = workspace.Info
local WaveEnded = info.WaveEnded
local LastWave = info.LastWave
local TweenService = game:GetService("TweenService")

local Wavecount

local round = {}
local votes = {}


local spawnedEnemies = {}  -- Initialize the table to track spawned enemies


local function ModifyConfiguration(enemy, modifiers)
	if not enemy or not enemy:FindFirstChild("Config") then
		warn("Enemy or enemy configuration is missing.")
		return
	end

	for modifier, value in pairs(modifiers) do
		local configModifier = enemy.Config:FindFirstChild(modifier)

		if configModifier then
			-- If the modifier already exists, update its value
			configModifier.Value = value
		else
			-- Create the modifier based on its type
			local newModifier
			if typeof(value) == "boolean" then
				newModifier = Instance.new("BoolValue")
			elseif typeof(value) == "number" then
				newModifier = Instance.new("NumberValue")
			else
				warn("Unsupported modifier type for:", modifier)
				continue
			end
			newModifier.Name = modifier
			newModifier.Value = value
			newModifier.Parent = enemy.Config
		end
	end
end

-- Function to tween any property of an object
local function TweenProperty(object, duration, property, endValue)
	task.spawn(function()


		local tweenInfo = TweenInfo.new(
			duration,
			Enum.EasingStyle.Linear,         -- Default easing style
			Enum.EasingDirection.Out         -- Default easing direction
		)

		-- Define the goal for the tween: setting `property` to `endValue`
		local goal = {[property] = endValue}

		-- Create and play the tween
		local tween = TweenService:Create(object, tweenInfo, goal)
		tween:Play()

		-- Return the tween object in case you want to manage it later
		return tween
	end)
end
local function CleanAllMusic()
	workspace.Music:ClearAllChildren()
end
local function PlayMusic(Music)
	if CurrentMusic == nil then
	local SelectedMusic = musics:FindFirstChild(Music):Clone()
	SelectedMusic.Parent = workspace.Music
	SelectedMusic:Play()
	CurrentMusic = SelectedMusic
	else
		CurrentMusic:Destroy()
		local SelectedMusic = musics:FindFirstChild(Music):Clone()
		SelectedMusic.Parent = workspace.Music
		SelectedMusic:Play()
		CurrentMusic = SelectedMusic
	end
end
local function StopMusic()
	if CurrentMusic ~= nil then
	TweenProperty(CurrentMusic, 2, "Volume" ,0)
	CurrentMusic:Stop()
	CurrentMusic:Destroy()
	CurrentMusic = nil
	end
end
-- Function to spawn an enemy, applying modifiers individually
local function SpawnEnemy(name, quanity, map, modifiers)
	task.spawn(function()
		if quanity == nil then
			quanity = 1
		end
		for i = 1, quanity do
			local newEnemy = mob.Spawn(name, 1, map)[1]  -- Spawn a single enemy

			if newEnemy then
				if modifiers then
					ModifyConfiguration(newEnemy, modifiers)
				end

				-- Add to spawnedEnemies table
				table.insert(spawnedEnemies, newEnemy)
			end
		end
	end)
end

-- Function to start the game
function round.StartGame()
	if info.GameRunning.Value == true then return end
	local waves = nil

	local map = round.LoadMap()  -- Load the map
	local mode = round.LoadMode()  -- Load the game mode

	-- Ensure mode and waves are set correctly
	if mode == "Easy" then
		waves = 30
	elseif mode == "Default" then
		waves = 40
	elseif mode == "Determined" then
		waves = 41
	elseif mode == "Unsettling" then
		waves = 3
	else
		-- Default fallback in case mode is not set properly
		warn("No valid mode selected, defaulting to 'Easy' mode.")
		waves = 5
	end

	Wavecount = waves -- Store wave count for later use
	info.GameRunning.Value = true

	for i = 3, 0, -1 do
		info.Message.Value = "Game starting in..." .. i
		task.wait(1)
	end

	-- Main wave loop
	for wave = 1, waves do
		info.Wave.Value = wave
		info.Message.Value = ""
		if mode == "Easy" then
			round.GetEasy(wave, map)
		elseif mode == "Default" then
			round.GetDefault(wave, map)
		elseif mode == "Determined" then
			round.GetDetermined(wave, map)
		elseif mode == "Unsettling" then
			round.GetUnsettling(wave, map)
		end

		-- Wait for the wave to complete
		repeat
			task.wait(1)
		until #workspace.Enemy:GetChildren() == 0 or not info.GameRunning.Value or (info.Min.Value <= 0 and info.Sec.Value <= 0)

		-- Check if the game has ended or if the last wave is finished
		if info.GameRunning.Value and wave == waves then
			info.Message.Value = "VICTORY"
			CleanAllMusic()
		elseif info.GameRunning.Value then
			
			local reward 
			if mode == "Easy" then
				reward = (50 * wave) * 0.75 / #Players:GetPlayers()
			elseif mode == "Default" then
				reward = (75 * wave) * 0.75 / #Players:GetPlayers()
			elseif mode == "Determined" then
				reward = (125 * wave) * 0.75 / #Players:GetPlayers()
			elseif mode == "Unsettling" then
				reward = (200 * wave) * 0.75 / #Players:GetPlayers()
			end
			
			for _, player in ipairs(Players:GetPlayers()) do
				player.Gold.Value += reward
			end
			WaveEnded.Value = true
			info.SkipAllowed.Value = false
			info.Message.Value = "Wave Reward: " .. reward
			task.wait(2)

			for i = 5, 0, -1 do
				info.Message.Value = "Next wave starting in..." .. i
				task.wait(1)
			end
		else
			break
		end
	end
end

-- Function to load the map
function round.LoadMap()
	local votedMap = round.ToggleVoting()

	if not votedMap then
		local maps = ServerStorage.Maps:GetChildren()
		votedMap = maps[math.random(1, #maps)].Name
	end

	local mapFolder = ServerStorage.Maps:FindFirstChild(votedMap)
	if not mapFolder then
		mapFolder = ServerStorage.Maps.Template
	end

	local newMap = mapFolder:Clone()
	newMap.Parent = workspace.Map

	workspace.SpawnBox.Floor:Destroy()
	local LightingDecor = newMap:FindFirstChild("LightingDecor")
	if LightingDecor then
		local sky = LightingDecor:FindFirstChild("Sky")
		if sky then
			sky.Parent = Lighting
		end
		for i, Value in pairs(LightingDecor:GetChildren()) do
			if Value:IsA("NumberValue") or Value:IsA("BoolValue") then
				if Value.Name == "FogEnd" then
					Lighting.FogEnd = Value.Value
				else
					warn("Value doesnt belong to Lighting")
				end
			end
		end
	end
	newMap.Base.Humanoid.HealthChanged:Connect(function(health)
		if health <= 0 then
			info.GameRunning.Value = false
			info.Message.Value = "GAME OVER"
			CleanAllMusic()
		end
	end)
	
	return newMap
end


function round.LoadMode()
	local votedMode = round.ToggleVotingMode()

	if not votedMode then

		votedMode = "Default"
	end
	info.Mode.Value = votedMode

	local modeFolder = ServerStorage.Modes:FindFirstChild(votedMode)
	if not modeFolder then

		modeFolder = ServerStorage.Modes.Default
	end

	return modeFolder.Name 
end


function round.ToggleVoting()
	local maps = ServerStorage.Maps:GetChildren()
	votes = {}
	for _, map in ipairs(maps) do
		votes[map.Name] = {}
	end

	info.Voting.Value = true

	for i = 15, 1, -1 do
		info.Message.Value = "Map voting (" .. i .. ")"
		task.wait(1)
	end

	local winVote = nil
	local winScore = 0
	for name, map in pairs(votes) do
		if #map > winScore then
			winScore = #map
			winVote = name
		end
	end

	info.Voting.Value = false

	return winVote
end


function round.ToggleVotingMode()
	local modes = ServerStorage.Modes:GetChildren()
	votes = {}
	for _, mode in ipairs(modes) do
		votes[mode.Name] = {}
	end

	info.GamemodeVoting.Value = true

	for i = 5, 1, -1 do
		info.Message.Value = "Gamemode voting (" .. i .. ")"
		task.wait(1)
	end

	local winVote = nil
	local winScore = 0
	for name, mode in pairs(votes) do
		if #mode > winScore then
			winScore = #mode
			winVote = name
		end
	end


	if not winVote then
		local randomMode = modes[math.random(1, #modes)]
		winVote = randomMode.Name
	end

	info.GamemodeVoting.Value = false

	return winVote
end


function round.ProcessVote(player, vote)
	for name, mapVotes in pairs(votes) do
		local oldVote = table.find(mapVotes, player.UserId)
		if oldVote then
			table.remove(mapVotes, oldVote)
			break
		end
	end

	table.insert(votes[vote], player.UserId)

	events:WaitForChild("UpdateVoteCount"):FireAllClients(votes)
end

events:WaitForChild("VoteForMap").OnServerEvent:Connect(round.ProcessVote)


function round.GetEasy(wave, map)
	if wave <= 2 then
		SpawnEnemy("Credulous", 2 * wave, map)
	elseif wave <= 4 then
		SpawnEnemy("Credulous", 3 * wave / 2, map)
		SpawnEnemy("Runner", 2 * wave / 2, map)
	elseif wave == 5 then
		SpawnEnemy("Credulous", 10, map)
		task.wait(1)
		SpawnEnemy("Prepared", 1, map)
		task.wait(1)
		SpawnEnemy("Prepared", 1, map)
		task.wait(2)
		SpawnEnemy("Runner", 4, map)
	elseif wave <= 9 then
		SpawnEnemy("Prepared", 3 * wave/3, map)
		task.wait(1)
		SpawnEnemy("Runner", 5 * (wave / 3), map)
		task.wait(1)
		SpawnEnemy("Credulous",1,map)
	elseif wave == 10 then
		SpawnEnemy("Prepared", 5, map)
		task.wait(3)
		SpawnEnemy("Prepared", 5, map)
		task.wait(4)
		SpawnEnemy("Champion",1,map, {IsBoss = true})
	elseif wave <= 12 then
		SpawnEnemy("Prepared", 5, map)
		task.wait(3)
		SpawnEnemy("Prepared", 5 * wave / 7, map)
		task.wait(4)
		SpawnEnemy("Credulous",2,map)
		task.wait(4)
		SpawnEnemy("Credulous",7,map)
	elseif wave == 13 then
		SpawnEnemy("Prepared", 5, map, {IsHidden = true})
		task.wait(3)
		SpawnEnemy("Credulous",1,map)
	elseif wave == 14 then
		SpawnEnemy("Champion", 1, map)
		task.wait(2)
		SpawnEnemy("Prepared",10,map)
	elseif wave == 15 then
		SpawnEnemy("Prepared", 20, map, {IsHidden = true})
	elseif wave <= 18 then
		SpawnEnemy("Armored", 5, map)
		task.wait(2)
		SpawnEnemy("Prepared", 5, map)
		task.wait(2)
		SpawnEnemy("Prepared", 3, map,{IsHidden = true})
		task.wait(2)
		SpawnEnemy("Champion", 1, map)
		task.wait(5)
		SpawnEnemy("Champion", 1, map)
	elseif wave == 19 then
		SpawnEnemy("Armored", 4, map)
		task.wait(2)
		SpawnEnemy("Prepared", 10, map)
		task.wait(1)
		SpawnEnemy("Champion", 1, map)
		task.wait(1)
		SpawnEnemy("Champion", 1, map)
		task.wait(2)
		SpawnEnemy("Swarmcore", 1, map,{IsBoss = true})
	elseif wave <= 21 then
		SpawnEnemy("Champion", 1, map)
		task.wait(1)
		SpawnEnemy("Champion", 1, map)
		task.wait(1)
		SpawnEnemy("Champion", 1, map)
		task.wait(1)
		SpawnEnemy("Champion", 1, map)
		task.wait(4)
		SpawnEnemy("Prepared", 7, map,{IsHidden = true})
	elseif wave <= 23 then
		SpawnEnemy("Champion", 2, map)
		task.wait(1)
		SpawnEnemy("Champion", 1, map)
		task.wait(4)
		SpawnEnemy("Shockwrecker", 1, map,{IsBoss = true})
	elseif wave == 24 then
		-- act 1
		SpawnEnemy("Champion", 2, map)
		task.wait(1)
		SpawnEnemy("Champion", 1, map)
		task.wait(4)
		SpawnEnemy("Raider", 1, map,{IsBoss = true})
		-- act 2
		task.wait(math.random(10,15))
		SpawnEnemy("Champion", 2, map)
		task.wait(1)
		SpawnEnemy("Champion", 1, map)
		task.wait(2)
		SpawnEnemy("Prepared", 4, map)
		task.wait(4)
		SpawnEnemy("Raider", 1, map)
	elseif wave <= 26 then
		SpawnEnemy("Champion", 2, map)
		task.wait(1)
		SpawnEnemy("Champion", 1, map)
		task.wait(1)
		SpawnEnemy("Champion", 1, map)
		task.wait(2)
		SpawnEnemy("Prepared", 9, map)
		task.wait(4)
		SpawnEnemy("Swarmcore", 2, map)
	elseif wave == 27 then
		SpawnEnemy("Champion", 2, map,{IsHidden = true})
		task.wait(1)
		SpawnEnemy("Raider", 1, map)
		task.wait(2)
		SpawnEnemy("Raider", 1, map)
		task.wait(2)
		SpawnEnemy("Prepared", 9, map,{IsHidden = true})
		task.wait(4)
		SpawnEnemy("Swarmcore", 1, map,{NoStun = true})
	elseif wave == 28 then
		PlayMusic("Reckoning")
		SpawnEnemy("Champion", 2, map,{IsHidden = true})
		task.wait(1)
		SpawnEnemy("Raider", 1, map)
		task.wait(2)
		SpawnEnemy("Raider", 1, map)
		task.wait(2)
		SpawnEnemy("Raider", 1, map)
		task.wait(4)
		SpawnEnemy("Sentinel", 1, map,{IsBoss = true})
	elseif wave == 29 then
		StopMusic()
		task.wait()
		info.Min.Value = 2
		SpawnEnemy("Champion", 10, map)
		task.wait(2)
		SpawnEnemy("Armed", 5,map)
		task.wait(2)
		SpawnEnemy("Runner",10,map)
		task.wait(2)
		SpawnEnemy("Champion", 5, map)
		task.wait(5)
		SpawnEnemy("Raider",2,map,{IsHidden = true})
		task.wait(10)
		--act 2
		SpawnEnemy("Champion", 5, map)
		task.wait(math.random(3,7))
		SpawnEnemy("Champion", 2, map)
		SpawnEnemy("Prepared", 10, map,{IsHidden = true})
		task.wait(3)
		SpawnEnemy("Armed",3,map)
	elseif wave == Wavecount then
		PlayMusic("Metal Storm")
		LastWave.Value = true
		SpawnEnemy("Champion", 7, map)
		task.wait(2)
		SpawnEnemy("Armed", 9, map)
		task.wait(1)
		SpawnEnemy("Prepared", 10)
		task.wait(6)
		SpawnEnemy("Raider", 2, map)
		task.wait(3)
		SpawnEnemy("Armored", 5, map)
		task.wait(4)
		SpawnEnemy("Eclipse Enforcer",1,map,{IsBoss = true, DefensePercent = 0})
	end
end


function round.GetDefault(wave, map)

	if wave <= 3 then
		SpawnEnemy("Credulous", 4 * wave / 2, map)
	elseif wave <= 5 then
		SpawnEnemy("Credulous", 3 * wave / 2, map)
		task.wait(2)
		SpawnEnemy("Runner", 3 * wave / 2, map)
	elseif wave == 6 then
		SpawnEnemy("Runner", 7, map)
		task.wait(5)
		SpawnEnemy("Prepared", 2, map)
		task.wait(5)
		SpawnEnemy("Prepared", 1, map)
	elseif wave <= 7 then
		SpawnEnemy("Armored", 2, map)
		task.wait(3)
		SpawnEnemy("Armored", 1, map)
	elseif wave == 8 then
		SpawnEnemy("Armored", 2, map) 
		task.wait(3)
		SpawnEnemy("Prepared", 4, map, {DefensePercent = 50})
	elseif wave == 9 then
		SpawnEnemy("Armored", 2, map)
		task.wait(1)
		SpawnEnemy("Armored", 2, map)
		task.wait(1)
		SpawnEnemy("Armored", 2, map)
		task.wait(1)
		SpawnEnemy("Armored", 2, map)
	elseif wave == 10 then
		-- Wave 9
		SpawnEnemy("Armored", 5, map) 
		task.wait(3)
		SpawnEnemy("Champion", 1, map)
		task.wait(10)
		SpawnEnemy("Prepared", 3, map) 

	elseif wave == 11 then
		SpawnEnemy("Armored",5,map,{DefensePercent = 50})
		task.wait(5)
		SpawnEnemy("Credulous", 10,map,{DefensePercent = 50})
		task.wait(2)
		SpawnEnemy("Chapion", 2, map)
	elseif wave <= 14 then
		SpawnEnemy("Armored", wave - 1, map) 
		task.wait(3)
		SpawnEnemy("Runner", wave / 2, map, {IsHidden = true})
		SpawnEnemy("Prepared",5,map)
	elseif wave == 15 then
		SpawnEnemy("Swarmcore", 1, map)
		task.wait(6)
		SpawnEnemy("Prepared", 5, map)
		task.wait(3)
		SpawnEnemy("Armed", 3, map)
	elseif wave <= 17 then
		SpawnEnemy("Champion", 2, map)
		task.wait(6)
		SpawnEnemy("Armed", 4 + (wave / 8), map)
		task.wait(3)
		SpawnEnemy("Armored", 7, map)
		task.wait(1)
		SpawnEnemy("Armored",2,map,{IsHidden = true})
		task.wait(3)
		SpawnEnemy("Swarmcore", 1, map, {IsHidden = true})
	elseif wave == 18 then
		SpawnEnemy("Shockwrecker", 1, map, {IsBoss = true})
		task.wait(6)
		SpawnEnemy("Armed", 4, map)
		task.wait(3)
		SpawnEnemy("Armored", 5, map, {DefensePercent = 80})
		task.wait(6)
		SpawnEnemy("Swarmcore", 1, map, {NoStun = true, DefensePercent = 40})
	elseif wave <= 21 then
		SpawnEnemy("Champion",3,map)
		SpawnEnemy("Armed", 4 + (wave/4), map)
		task.wait(3)
		SpawnEnemy("Armored", 5, map, {DefensePercent = 80})
		task.wait(6)
		SpawnEnemy("Swarmcore", 1, map, {NoStun = true, DefensePercent = 40})
	elseif wave == 22 then
		SpawnEnemy("Shockwrecker",2,map)
		task.wait(2)
		SpawnEnemy("Armored", 5, map)
		task.wait(5)
		SpawnEnemy("Raider", 5, map, {IsHidden = true})
	elseif wave <= 25 then
		SpawnEnemy("Bomber", 2,map)
		SpawnEnemy("Prepapred",15,map)
		task.wait(4)
		SpawnEnemy("Armed",3,map)
		SpawnEnemy("Swarmcore",3,map)
	elseif wave == 26 then
		SpawnEnemy("Swarmcore",5,map)
		SpawnEnemy("Prepared",10,map)
		task.wait(5)
		SpawnEnemy("Sentinel",1,map,{IsBoss = true})
		task.wait(15)
		SpawnEnemy("Raider",1,map)
		task.wait(1)
		SpawnEnemy("Raider",1,map)
		task.wait(1)
		SpawnEnemy("Raider",1,map)
		SpawnEnemy("Runner",1,map)
	elseif wave <= 29 then
		SpawnEnemy("Champion",9, map, {IsHidden = true})
		task.wait(2)
		SpawnEnemy("Bomber",1,map)
		SpawnEnemy("Swarmcore",2,map)
		task.wait(3)
		SpawnEnemy("Raider",4,map)
		SpawnEnemy("Armed",1 * (wave / 4),map)
	elseif wave == 30 then
		task.wait(0.1)
		info.Min.Value = 3
		PlayMusic("Chaos Reigns")
		SpawnEnemy("Bomber", 6,map)
		task.wait(24)
		SpawnEnemy("Sentinel", 2, map, {NoMoney = true})
		task.wait(5)
		SpawnEnemy("Arcanos servant",3,map)
		task.wait(3)
		SpawnEnemy("Raider",5,map)
		task.wait(7)
		SpawnEnemy("Prepared", 8, map, {IsHidden = true, DefensePercent = 80, NoStun = true})
		SpawnEnemy("Armed",5,map)
		SpawnEnemy("Armored", 10, map)
		task.wait(10)
		SpawnEnemy("Shockwrecker",3,map)
		SpawnEnemy("Raider",2,map)
		task.wait(2)
		SpawnEnemy("Bomber",4,map)
	elseif wave == 31 then
		SpawnEnemy("Arcanos servant",2,map)
		task.wait(2)
		SpawnEnemy("Arcanos servant",2,map)
		SpawnEnemy("Raider",5,map)
		task.wait(3)
		SpawnEnemy("Sentinel",1,map)
	elseif wave == 32 then
		task.wait(0.1)
		info.Min.Value = 2
		SpawnEnemy("Arcanos servant",2,map)
		SpawnEnemy("Armed",5,map)
		SpawnEnemy("Armored", 10, map)
		task.wait(10)
		SpawnEnemy("Shockwrecker",2,map)
		SpawnEnemy("Raider",2,map)
		task.wait(2)
		SpawnEnemy("Bomber",4,map)
		task.wait(1)
		SpawnEnemy("Shockwrecker",1,map)
		SpawnEnemy("Subjugated vanguard", 1, map)
	elseif wave <= 34 then
		SpawnEnemy("Bomber",10,map)
		SpawnEnemy("Raider",5,map)
		task.wait(2)
		SpawnEnemy("Sentinel",2,map)
	elseif wave == 35 then
		PlayMusic("Intense Chase")
		task.wait(0.1)
		info.Min.Value = 4
		SpawnEnemy("Sentinel",1,map, {NoMoney = true})
		task.wait(3)
		SpawnEnemy("Sentinel",1,map, {NoMoney = true})
		task.wait(3)
		SpawnEnemy("Sentinel",1,map, {NoMoney = true})
		task.wait(5)
		SpawnEnemy("Eclipse Enforcer",1,map, {IsBoss = true})
		task.wait(7)
		SpawnEnemy("Eclipse Enforcer",1,map, {IsBoss = true})
		SpawnEnemy("Bomber",2,map)
		task.wait(2)
		SpawnEnemy("Arcanos servant",2,map)
		task.wait(3)
		SpawnEnemy("Champion",10,map)
	elseif wave <= 38 then
		StopMusic()
		SpawnEnemy("Champion", 20 + ((wave - 37)*-1), map)
		SpawnEnemy("Arcanos servant",1,map)
		task.wait(2)
		SpawnEnemy("Swarmcore", 3 + (wave / 7),map)
		task.wait(1)
		SpawnEnemy("Shockwrecker",5,map)
	elseif wave == 39 then
		SpawnEnemy("Subjugated vanguard", 1, map)
		task.wait(4)
		SpawnEnemy("Bomber",4,map)
		task.wait(5)
		SpawnEnemy("Arcanos servant",8,map)
		task.wait(9)
		SpawnEnemy("Arcane sentinel", 1, map)
	elseif wave == Wavecount then
		LastWave.Value = true
		PlayMusic("Metal Storm")
		SpawnEnemy("Arcanos servant",4,map)
		task.wait(4)
		SpawnEnemy("Subjugated vanguard", 2, map)
		SpawnEnemy("Raider",3,map)
		task.wait(5)
		SpawnEnemy("Arcanos servant",5,map)
		SpawnEnemy("Arcanos servant",2,map)
		task.wait(4)
		SpawnEnemy("Arcanos Prime",1, map, {IsBoss = true})
		task.wait(5)
		SpawnEnemy("Arcane sentinel", 1, map)
	end
end

function round.GetDetermined(wave, map)	
	if wave <= 2 then
		SpawnEnemy("Startler", 3 * (wave / 1.5), map)
	elseif wave <= 5 then
		SpawnEnemy("Rusher", 4 + (wave / 2) , map)
		task.wait(5)
		SpawnEnemy("Startler", 4 + (wave / 2) , map)
		task.wait(6)
		if wave == 5 then
			SpawnEnemy("Controlled",1,map)
		end
	elseif wave == 6 then
		SpawnEnemy("Prepared", 5, map)
		task.wait(5)
		SpawnEnemy("Armed",2,map)
		task.wait(2)
		SpawnEnemy("Rusher",7,map)
		task.wait(5)
		SpawnEnemy("Controlled",1,map)
	elseif wave <= 9 then
		SpawnEnemy("Controlled",(wave / 2),map)
		task.wait(5)
		SpawnEnemy("Rusher",10,map)
		task.wait(4)
		SpawnEnemy("Armed",2,map)
		SpawnEnemy("Startler",5,map)
		if wave == 9 then
			task.wait(0.2)
			SpawnEnemy("Startler",5,map)
			SpawnEnemy("Rusher",10)
		end
	elseif wave == 10 then
		SpawnEnemy("Champion",2,map)
		SpawnEnemy("Startler",10,map)
		task.wait(3)
		SpawnEnemy("Prepared",5,map)
		task.wait(5)
		SpawnEnemy("Controlled",7,map)
		task.wait(4)
		SpawnEnemy("Chained",1,map)
	elseif wave <= 13 then
		SpawnEnemy("Controlled",10,map)
		task.wait(2)
		SpawnEnemy("Champion",2,map)
		task.wait(4)
		SpawnEnemy("Armed",3,map)
		if wave == 13 then
			task.wait(0.2)
			SpawnEnemy("Rusher",10,map,{IsHidden = true})
			SpawnEnemy("Startler",5,map)
		end
	elseif wave == 14 then
		SpawnEnemy("Chained",2,map)
		task.wait(4)
		SpawnEnemy("Raider",2,map,{NoStun = true})
		task.wait(3)
		SpawnEnemy("Swarmcore",3,map)
		task.wait(2)
		SpawnEnemy("Controlled",2,map,{IsHidden = true})
		SpawnEnemy("Rusher",10,map)
	elseif wave <= 17 then
		info.Min.Value = 1
		SpawnEnemy("Swarmcore",1,map)
		SpawnEnemy("Controlled",2,map)
		task.wait(4)
		SpawnEnemy("Chained",2,map)
		task.wait(7)
		SpawnEnemy("Controlled",6,map)
		if wave == 16 then
			task.wait(0.2)
			SpawnEnemy("Raider",5,map)
			SpawnEnemy("Controlled",7,map)
		end
		if wave == 17 then
			task.wait(0.2)
			SpawnEnemy("Swarmcore",2,map)
			SpawnEnemy("Controlled",7,map)
			task.wait(5)
			SpawnEnemy("Chained",1,map)
		end
	elseif wave == 18 then
		task.wait(1)
		info.Min.Value = 3
		PlayMusic("Lip Service")
		SpawnEnemy("Chained",2,map)
		task.wait(2)
		SpawnEnemy("Chained",2,map)
		SpawnEnemy("Controlled",5,map)
		task.wait(4)
		SpawnEnemy("Enforcer",1,map, {IsBoss = true})
		task.wait(5)
		SpawnEnemy("Rusher", 20,map)
		task.wait(2)
		SpawnEnemy("Chained",1,map)
	elseif wave <= 20 then
		SpawnEnemy("Chained",4,map)
		SpawnEnemy("Controlled",6,map,{Fortified = true})
		task.wait(8)
		SpawnEnemy("Sentinel",1,map)
		task.wait(3)
		if wave == 20 then
			SpawnEnemy("Champion",9,map,{Fortified = true,Fleetfooted = true})
		end
	elseif wave <= 24 then
		SpawnEnemy("Bomber",2,map)
		if wave == 23 then
			SpawnEnemy("Champion",3,map,{Fortified = true})
		end
		task.wait(3)
		SpawnEnemy("Chained",2,map)
		task.wait(5)
		SpawnEnemy("Shockwrecker",1,map,{Fortified = true})
		if wave == 23 or wave == 22 then
			SpawnEnemy("Bomber",2,map)
		end
		task.wait(1)
		SpawnEnemy("Swarmcore",2,map)
		if wave == 24 then
			SpawnEnemy("Sentinel",1,map)
		end
	elseif wave == 25 then
		SpawnEnemy("Sentinel",2,map, {IsHidden = true})
		SpawnEnemy("Bomber",5,map)
		task.wait(3)
		SpawnEnemy("Swarmcore",2,map)
		task.wait(3)
		SpawnEnemy("Bound Runner",7,map)
		task.wait(2)
		SpawnEnemy("Chained",3,map)
	elseif wave < 29 then
		SpawnEnemy("Sentinel",1,map)
		SpawnEnemy("Bound Runner",5,map)
		task.wait(5)
		if wave == 27 or wave == 28 then
			SpawnEnemy("Bomber",2,map)
			task.wait(2)
			SpawnEnemy("Chained",6,map,{Fortified = true})
			task.wait(2)
		end
		SpawnEnemy("Raider",6,map)
		task.wait(2)
		SpawnEnemy("Shockwrecker",2,map)
		if wave == 29 then
		SpawnEnemy("Enforcer",1,map)
		end
	elseif wave == 30 then
		PlayMusic("FFU")
		info.Min.Value = 3
		SpawnEnemy("Bound Runner",7,map,{Fleedfooted = true})
		task.wait(5)
		SpawnEnemy("Bomber",4,map,{Fortified = true})
		task.wait(3)
		SpawnEnemy("Controlled",10,map,{Fortified = true,Fleetfooted = true})
		SpawnEnemy("Bound Runner",2,map,{Fortified = true})
		SpawnEnemy("Sentinel",1,map,{Fortified = true})
		task.wait(4)
		SpawnEnemy("Swarmcore",4,map,{Fortified = true})
		SpawnEnemy("Bomber",1,map,{Fortified = true})
		task.wait(1)
		SpawnEnemy("Chained",1,map,{Fortified = true})
		task.wait(2)
		SpawnEnemy("Arcanos servant",2,map)
		task.wait(4)
		SpawnEnemy("Shockwrecker",5,map,{Fortified = true})
		task.wait(6)
		SpawnEnemy("Sentinel",2,map)
		task.wait(2)
		SpawnEnemy("Chained",6,map,{Fortified = true})
		SpawnEnemy("Enforcer",1,map)
		SpawnEnemy("Bound Runner",4,map)
		task.wait(10)
		SpawnEnemy("Enforcer",2,map, {IsHidden = true})
		task.wait(2)
		SpawnEnemy("Arcanos servant",2,map)
		SpawnEnemy("Bomber",2,map)
		SpawnEnemy("Cultist",1,map)
		SpawnEnemy("Swarmcore",4,map,{Fortified = true})
		task.wait(6)
		SpawnEnemy("Arcanos servant",2,map,{Fortified = true})
		SpawnEnemy("Sentinel",5,map)
		SpawnEnemy("Enforcer",2,map, {IsHidden = true})
		SpawnEnemy("Rusher",20,map, {IsHidden = true,Fortified = true, Fleetfooted = true})
		SpawnEnemy("Bound Runner",2,map)
		SpawnEnemy("Cultist",1,map)
		task.wait(10)
		SpawnEnemy("Controlled",10,map,{Fortified = true})
		SpawnEnemy("Controlled",3,map,{Fortified = true})
		SpawnEnemy("Arcanos servant",3,map)
		task.wait(2)
		SpawnEnemy("Chained",5,map,{Fortified = true})
		SpawnEnemy("Controlled",6,map,{Fortified = true,Fleetfooted = true})
		SpawnEnemy("Arcanos servant",1,map,{Fortified = true,Fleetfooted = true})
	elseif wave <= 33 then
		task.wait(1)
		info.Min.Value = 3
		SpawnEnemy("Cultist",1,map,{Fortified = true})
		SpawnEnemy("Bound Runner",13,map)
		SpawnEnemy("Arcanos servant",2,map)
		SpawnEnemy("Chained",9,map,{Fortified = true})
		SpawnEnemy("Chained",10,map)
		task.wait(3)
		SpawnEnemy("Controlled",7,map)
		SpawnEnemy("Cultist",1,map)
		task.wait(5)
		if wave == 32 then
		SpawnEnemy("Eclipse Enforcer",1,map,{NoMoney = true, IsBoss = true})
		SpawnEnemy("Cultist",1,map)
		end
		SpawnEnemy("Swarmcore",3,map,{Fortified = true})
		task.wait(2)
		if wave == 32 or wave == 33 then
			SpawnEnemy("Chained",9,map,{Fortified = true})
			SpawnEnemy("Bound Runner",5,map)
			SpawnEnemy("Cultist",2,map)
			
		end
		task.wait(5)
		if wave == 33 then
			SpawnEnemy("Subjugated vanguard",3,map)
			SpawnEnemy("Chained",9,map,{Fortified = true})
			SpawnEnemy("Enforcer",5,map,{IsHidden = true, NoMoney = true,Fortified = true})
		end
	elseif wave <= 35 then
		task.wait(1)
		info.Min.Value = 3
		SpawnEnemy("Eclipse Enforcer",1,map,{NoMoney = true, IsBoss = true})
		task.wait(2)
		SpawnEnemy("Bound Runner",13,map,{Fortified = true})
		SpawnEnemy("Subjugated vanguard",1,map,{Fortified = true})
		SpawnEnemy("Chained",9,map,{Fortified = true})
		SpawnEnemy("Cultist",1,map,{DefensePercent = 30})
		SpawnEnemy("Enforcer",5,map,{IsHidden = true, NoMoney = true, Fortified = true})
		task.wait(5)
		if wave == 35 then
			SpawnEnemy("Bomber",1,map)
			SpawnEnemy("Controlled",10,map,{Fortified = true,Fleetfooted = true})
		end
		SpawnEnemy("Chained",9,map,{Fortified = true})

		SpawnEnemy("Bomber",1,map,{Fortified = true})
		task.wait(14)
		SpawnEnemy("Eclipse Enforcer",1,map,{NoMoney = true, IsBoss = true})
		SpawnEnemy("Controlled",10,map,{Fortified = true,Fleetfooted = true})
		task.wait(10)
		SpawnEnemy("Bound Runner",10,map,{Fortified = true})
		SpawnEnemy("Arcanos servant",5,map)
		SpawnEnemy("Eclipse Enforcer",1,map,{NoMoney = true, IsBoss = true,IsHidden = true})
		if wave == 34 then
			SpawnEnemy("Bomber",2,map)
			SpawnEnemy("Controlled",10,map,{Fortified = true,Fleetfooted = true})
		end
		task.wait(2)
		SpawnEnemy("Enforcer",2,map, {IsHidden = true,Fortified = true})
		SpawnEnemy("Bomber",1,map,{Fortified = true,Fleetfooted = true})
		SpawnEnemy("Arcanos servant",1,map,{Fortified = true})
	elseif wave <= 37 then
		task.wait(1)
		info.Min.Value = 3
		SpawnEnemy("Subjugated vanguard",3,map)
		SpawnEnemy("Bomber",1,map,{Fortified = true,Fleetfooted = true})
		task.wait(2)
		SpawnEnemy("Bound Runner",wave,map,{Fortified = true})
		SpawnEnemy("Subjugated vanguard",1,map)
		SpawnEnemy("Chained",9,map,{Fortified = true,Fleetfooted = true})
		SpawnEnemy("Cultist",1,map,{DefensePercent = 30})
		SpawnEnemy("Eclipse Enforcer",2,map,{IsHidden = true, NoMoney = true})
		task.wait(5)
		if wave == 37 then
			SpawnEnemy("Bomber",1,map,{Fortified = true,Fleetfooted = true})
			SpawnEnemy("Cultist",1,map,{DefensePercent = 30})
			SpawnEnemy("Subjugated vanguard",4,map)
		end
		if wave == 36 then
			SpawnEnemy("Bomber",10,map)
			SpawnEnemy("Cultist",1,map,{DefensePercent = 30})
			SpawnEnemy("Subjugated vanguard",4,map)
		end
	elseif wave <= 39 then
		task.wait(1)
		info.Min.Value = 3
		if wave == 39 then
		SpawnEnemy("Arcanos Prime",1,map,{IsBoss = true})
		end
		SpawnEnemy("Eclipse Enforcer",1,map)
		SpawnEnemy("Bound Runner",12,map,{Fortified = true,Fleetfooted = true})
		SpawnEnemy("Subjugated vanguard",1,map)
		task.wait(13)
		SpawnEnemy("Cultist",1,map)
	elseif wave == 40 then
		task.wait(1)
		info.Min.Value = 10
		PlayMusic("Chaos Engine")
		SpawnEnemy("Eclipse Enforcer",1,map,{Fleetfooted = true})
		task.wait(4)
		SpawnEnemy("Chained",15,map,{Fortified = true})	
		task.wait(2)
		SpawnEnemy("Purity",1,map,{IsBoss = true})
		task.wait(4)
		SpawnEnemy("Chained",5,map,{Fortified = true})
	elseif wave == Wavecount then
		info.LastWave.Value = true
		PlayMusic("Full Force")
		SpawnEnemy("Bomber",12,map,{IsHidden = true, NoMoney = true, DefensePercent = 50, Fortified = true, Fleetfooted = true, NoStun = true})
		task.wait(6)
		SpawnEnemy("Controlled",20,map,{IsHidden = true, NoMoney = true, DefensePercent = 50, Fortified = true, Fleetfooted = true, NoStun = true})
		SpawnEnemy("Chained",8,map,{IsHidden = true, NoMoney = true, DefensePercent = 50, Fortified = true, NoStun = true})
		task.wait(6)
		SpawnEnemy("Arcanos servant",3,map)
		task.wait(4)
		SpawnEnemy("Cultist",3,map, {DefensePercent = 10, IsHidden = true})
		task.wait(7)
		SpawnEnemy("Eclipse Enforcer",1,map)
		SpawnEnemy("Arcanos servant",7,map)
		task.wait(5)
		SpawnEnemy("Chained",7,map,{Fortified = true})
		SpawnEnemy("Bounded Runner",5,map,{Fortified = true})
		task.wait(5) 
		SpawnEnemy("Arcanos servant",2,map)
		task.wait(2)
		SpawnEnemy("Bound Runner",5,map,{Fortified = true})
		SpawnEnemy("Bomber",2,map)
		SpawnEnemy("Swarmcore",4,map,{Fortified = true, Fleetfoot = true})
		task.wait(5)
		SpawnEnemy("Chained",7,map,{Fortified = true})
		task.wait(5)
		SpawnEnemy("Sentinel",4,map, {Fortified = true, Fleetfooted = true})
		SpawnEnemy("Chained",5,map,{Fortified = true})
		task.wait(7)
		SpawnEnemy("Raider",3,map,{Fortified = true, Fleetfooted = true})
		SpawnEnemy("Bomber",2,map,{IsHidden = true, NoMoney = true, DefensePercent = 50, Fortified = true, Fleetfooted = true, NoStun = true})
		task.wait(4)
		SpawnEnemy("Raider",3,map,{Fortified = true, Fleetfooted = true})
		task.wait(4)
		SpawnEnemy("Cultist",1,map)
		SpawnEnemy("Bomber",2,map,{IsHidden = true, NoMoney = true, DefensePercent = 50, Fortified = true, Fleetfooted = true, NoStun = true})
		task.wait(2)
		SpawnEnemy("Controlled",10,map,{Fortified = true, Fleetfooted = true})
		task.wait(7)
		SpawnEnemy("Enforcer",3,map,{Fortified = true, Fleetfooted = true})
		SpawnEnemy("Arcanos servant",2,map)
		task.wait(5)
		SpawnEnemy("Chained",6,map,{Fortified = true, Fleetfooted = true})
		task.wait(3)
		SpawnEnemy("Swarmcore",4,map,{Fortified = true, Fleetfoot = true})
		SpawnEnemy("Chained",5,map,{Fortified = true})
		task.wait(3)
		SpawnEnemy("Arcanos servant",7,map)
		task.wait(5)
		SpawnEnemy("Shockwrecker",5,map,{Fortified = true, Fleetfooted = true})
		SpawnEnemy("Bomber",5,map,{Fortified = true, Fleetfooted = true})
		task.wait(10)
		SpawnEnemy("Enforcer",1,map,{Fortified = true, Fleetfooted = true,IsHidden = true})
		task.wait(5) 
		SpawnEnemy("Chained",5,map,{Fortified = true})
		SpawnEnemy("Enforcer",2,map,{Fortified = true, Fleetfooted = true,IsHidden = true})
		task.wait(3)
		SpawnEnemy("Chained",6,map,{Fortified = true})
		task.wait(3)
		SpawnEnemy("Chained",5,map,{Fortified = true})
		SpawnEnemy("Bound Runner",10,map,{Fortified = true})
		task.wait(4)
		SpawnEnemy("Controlled",10,map,{IsHidden = true, NoMoney = true, DefensePercent = 50, Fortified = true, Fleetfooted = true, NoStun = true})
		task.wait(5)
		SpawnEnemy("Chained",6,map,{Fortified = true})
		SpawnEnemy("Eclipse Enforcer",1,map,{NoStun = true})
		SpawnEnemy("Cultist",1,map)
		task.wait(2)
		SpawnEnemy("Chained",5,map,{Fortified = true})
		SpawnEnemy("Controlled",10,map,{IsHidden = true, NoMoney = true, DefensePercent = 50, Fortified = true, Fleetfooted = true, NoStun = true})
		SpawnEnemy("Enforcer",2,map,{Fortified = true})
		task.wait(4)
		SpawnEnemy("Bomber",4,map)
		SpawnEnemy("Arcane Servant",4,map)
		task.wait(2)
		SpawnEnemy("Chained",6,map,{Fortified = true})
		SpawnEnemy("Cultist",1,map)
		SpawnEnemy("Enforcer",2,map,{Fortified = true})
		task.wait(6)
		SpawnEnemy("Chained",9,map,{Fortified = true})
		SpawnEnemy("Enforcer",2,map,{Fortified = true})
		task.wait(8)
		SpawnEnemy("Bomber",2,map,{IsHidden = true, NoMoney = true, DefensePercent = 50, Fortified = true, Fleetfooted = true, NoStun = true})
		task.wait(2)
		SpawnEnemy("Arcanos servant",7,map)
		SpawnEnemy("Eclipse Enforcer",1,map)
		task.wait(10)
		SpawnEnemy("Arcanos servant",1,map)
		SpawnEnemy("Bomber",2,map,{IsHidden = true, NoMoney = true, DefensePercent = 50, Fortified = true, Fleetfooted = true, NoStun = true})
		SpawnEnemy("Swarmcore",2,map,{IsHidden = true, NoMoney = true, DefensePercent = 50, Fortified = true, Fleetfooted = true, NoStun = true})
		task.wait(4)
		SpawnEnemy("Bound Runner",14,map,{IsHidden = true, NoMoney = true, DefensePercent = 50, Fortified = true, NoStun = true})
		task.wait(1)
		SpawnEnemy("Raider",5,map,{IsHidden = true, NoMoney = true, DefensePercent = 50, Fortified = true, Fleetfooted = true, NoStun = true})
		SpawnEnemy("Enforcer",3,map,{Fortified = true, Fleetfooted = true,IsHidden = true})
		task.wait(10)
		SpawnEnemy("Cultist",1,map)
		SpawnEnemy("Bound Runner",7,map)
		SpawnEnemy("Bomber",2,map,{IsHidden = true, NoMoney = true, DefensePercent = 50, Fortified = true, Fleetfooted = true, NoStun = true})
		SpawnEnemy("Enforcer",4,map)
		task.wait(6)
		SpawnEnemy("Eclipse Enforcer",1,map)
		SpawnEnemy("Bomber",2,map,{IsHidden = true, NoMoney = true, DefensePercent = 50, Fortified = true, Fleetfooted = true, NoStun = true})
		SpawnEnemy("Controlled",20,map,{Fortified = true, Fleetfooted = true})
		SpawnEnemy("Enforcer",4,map,{Fortified = true})
		SpawnEnemy("Swarmcore",2,map,{IsHidden = true, NoMoney = true, DefensePercent = 50, Fortified = true, Fleetfooted = true, NoStun = true})
		task.wait(15)
		SpawnEnemy("Cultist",3,map,{IsHidden = true})
		task.wait(2)
		SpawnEnemy("Swarmcore",5,map,{Fortified = true, Fleetfooted = true})
		-- end here
	end
end

function round.GetUnsettling(wave, map)
	for i, v in pairs(game.Players:GetPlayers()) do
		v.Gold.Value += 4952345234
	end
		if wave <= 2 then
		SpawnEnemy("Spawn3",5,map,{Fortified = true,Fleetfooted = true})
		task.wait(3)
		SpawnEnemy("Walahe",1,map)
	elseif wave == 3 then
		LastWave.Value = true
		SpawnEnemy("Credulous", 4 * wave, map)
		task.wait(2)
		SpawnEnemy("Credulous", 4 * wave, map)
	end
end

return round
