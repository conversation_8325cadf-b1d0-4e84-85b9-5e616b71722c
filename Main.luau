local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

local mob = require(script.Enemy)
local unit = require(script.Unit)
local tower = require(script.Tower)
local round = require(script.Round)

local events = ReplicatedStorage:WaitForChild("Events")

local minPlayers = 1
local readyToStart = true

Players.PlayerAdded:Connect(function(player)
	local currentPlayers = #Players:GetPlayers()

	if currentPlayers >= minPlayers and readyToStart then
		readyToStart = false
		round.StartGame()
		readyToStart = true
	else
		workspace.Info.Message.Value = "Waiting for " .. (minPlayers - currentPlayers) .. " more player(s)"
	end
end)

game.ReplicatedStorage.Events.BossEvent.OnServerEvent:Connect(function(Child)
	local Config = Child:FindFirstChild("Config")
	if Config then
	local Bool = Config:FindFirstChild("IsBoss")
	if <PERSON><PERSON> and <PERSON><PERSON>.Value == true then
		Bool.Value = false 
	elseif Bool.Value == false then
	return
		end
	end
end)