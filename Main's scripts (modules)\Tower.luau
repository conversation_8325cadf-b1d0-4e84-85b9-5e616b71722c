local PhysicsService = game:GetService("PhysicsService")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")
local DebrisService = game:GetService("Debris")
local TweenService = game:GetService("TweenService")
local Players = game:GetService("Players")

local events = ReplicatedStorage:WaitForChild("Events")
local animateTowerEvent = events:WaitForChild("AnimateTower")
local animateTowerChargeEvent = events:WaitForChild("AnimateTowerCharge")
local messageEvent = events:WaitForChild("MessageEvent")
local functions = ReplicatedStorage:WaitForChild("Functions")
local spawnTowerFunction = functions:WaitForChild("SpawnTower")
local requestTowerFunction = functions:WaitForChild("RequestTower")
local sellTowerFunction = functions:WaitForChild("SellTower")
local changeModeFunction = functions:WaitForChild("ChangeTowerMode")

local Particles = ReplicatedStorage:WaitForChild("Particles")

local maxTowers = 20
local tower = {}
local IndividualTowersOnLimit = {}

local function SendCash(player, value, allowed)
	if allowed == nil then
		allowed = true
	end
	if allowed == true then
		for i, plr in pairs(game.Players:GetPlayers()) do
			if plr ~= player then
				plr.Gold.Value += (value / #game.Players:GetPlayers() / 2)
			end
		end
	end
	player.Gold.Value += value
end

function tower.FindTarget(newTower, range, mode)
	local bestTarget = nil
	local bestWaypoint = nil
	local bestDistance = nil
	local bestHealth = nil
	local map = workspace.Map:FindFirstChildOfClass("Folder")
	local enemies = workspace.Enemy:GetChildren()

	for i, enemy in ipairs(enemies) do
		local IsHidden = enemy.Config:FindFirstChild("IsHidden")
		local HiddenDetection = newTower.Config.HiddenDetection



		local distanceToEnemy = (enemy.HumanoidRootPart.Position - newTower.HumanoidRootPart.Position).Magnitude
		local distanceToWaypoint = (enemy.HumanoidRootPart.Position - map.Waypoints[enemy.MovingTo.Value].Position).Magnitude

		if distanceToEnemy <= range and enemy.Humanoid.Health > 0 then
			if IsHidden and IsHidden.Value == true and HiddenDetection.Value == true or not IsHidden  or IsHidden.Value == false then
				if mode == "Nearest" then
					range = distanceToEnemy
					bestTarget = enemy
				elseif mode == "First" then
					-- Ensure the enemy has valid MovingTo.Value and distanceToWaypoint is available
					if enemy.MovingTo and enemy.MovingTo.Value and distanceToWaypoint then

						-- If we don't have a best target yet or the current enemy is further along the path
						if not bestWaypoint or enemy.MovingTo.Value > bestWaypoint then
							bestWaypoint = enemy.MovingTo.Value
							bestDistance = distanceToWaypoint
							bestTarget = enemy

							-- If waypoint is the same, prioritize based on distance to waypoint
						elseif enemy.MovingTo.Value == bestWaypoint and distanceToWaypoint < bestDistance then
							bestDistance = distanceToWaypoint
							bestTarget = enemy
						end
					end
				elseif mode == "Last" then
					-- Ensure the enemy has valid MovingTo.Value and distanceToWaypoint is available
					if enemy.MovingTo and enemy.MovingTo.Value and distanceToWaypoint then

						-- If we don't have a best target yet or the current enemy is further along the path
						if not bestWaypoint or enemy.MovingTo.Value < bestWaypoint then
							bestWaypoint = enemy.MovingTo.Value
							bestDistance = distanceToWaypoint
							bestTarget = enemy

							-- If waypoint is the same, prioritize based on distance to waypoint
						elseif enemy.MovingTo.Value == bestWaypoint and distanceToWaypoint > bestDistance then
							bestDistance = distanceToWaypoint
							bestTarget = enemy
						end
					end
				elseif mode == "Strongest" then
					-- Ensure health is valid
					if enemy.Humanoid and enemy.Humanoid.Health and enemy.MovingTo and enemy.MovingTo.Value then

						-- If we don't have a best target yet, or the current enemy has more health
						if not bestHealth or enemy.Humanoid.Health > bestHealth then
							bestHealth = enemy.Humanoid.Health
							bestWaypoint = enemy.MovingTo.Value
							bestDistance = distanceToWaypoint
							bestTarget = enemy

							-- If health is the same, prioritize based on waypoint or distance
						elseif enemy.Humanoid.Health == bestHealth then
							if not bestWaypoint or enemy.MovingTo.Value > bestWaypoint then
								bestWaypoint = enemy.MovingTo.Value
								bestTarget = enemy
							elseif enemy.MovingTo.Value == bestWaypoint and distanceToWaypoint and distanceToWaypoint < bestDistance then
								bestDistance = distanceToWaypoint
								bestTarget = enemy
							end
						end
					end
				elseif mode == "Weakest" then
					if enemy.Humanoid and enemy.Humanoid.Health then

						if not bestHealth or enemy.Humanoid.Health < bestHealth then
							bestHealth = enemy.Humanoid.Health
							bestTarget = enemy
						end
					end
				elseif mode == "Random" then
					repeat
						local rng = math.random(#enemies)

						local distanceToMob2 = (enemies[rng].HumanoidRootPart.Position - newTower.HumanoidRootPart.Position).magnitude

						if distanceToMob2 <= range then
							bestTarget = enemies[rng]
						end
					until bestTarget ~= nil
				end



			end
		end
	end

	return bestTarget
end 


function tower.Optimize(towerToOptimize)
	local humanoid = towerToOptimize:FindFirstChild("Humanoid")

	if towerToOptimize:FindFirstChild("HumanoidRootPart") then 
		towerToOptimize.HumanoidRootPart:SetNetworkOwner(nil) 
	elseif towerToOptimize.PrimaryPart ~= nil then
		towerToOptimize.PrimaryPart:SetNetworkOwner(nil)
	end
	if not humanoid then return end
	humanoid:SetStateEnabled(Enum.HumanoidStateType.Seated, false)
	humanoid:SetStateEnabled(Enum.HumanoidStateType.Jumping, false)
	humanoid:SetStateEnabled(Enum.HumanoidStateType.Running, false)
	humanoid:SetStateEnabled(Enum.HumanoidStateType.GettingUp, false)
	humanoid:SetStateEnabled(Enum.HumanoidStateType.Climbing, false)
	humanoid:SetStateEnabled(Enum.HumanoidStateType.Landed, false)
	humanoid:SetStateEnabled(Enum.HumanoidStateType.Ragdoll, false)
	humanoid:SetStateEnabled(Enum.HumanoidStateType.Freefall, false)
end

function tower.Stun(tower, target, duration)
	local StunEffect = Particles:FindFirstChild("StunEffect"):Clone()
	local stunAnim = ReplicatedStorage:WaitForChild("MiscAnims"):WaitForChild("StunAnim")
	local humanoid = target:FindFirstChild("Humanoid")
	local animator = humanoid:FindFirstChildOfClass("Animator") or Instance.new("Animator", humanoid)
	local animationTrack = animator:LoadAnimation(stunAnim)

	if humanoid then
		if not target.Torso:FindFirstChild("StunEffect")or target.HumanoidRootPart:FindFirstChild("StunEffect") then
			StunEffect.Parent = target.Torso or target.HumanoidRootPart
		end
		humanoid.WalkSpeed = humanoid.WalkSpeed / 5

		animationTrack:Play()



		spawn(function()
			task.wait(duration)
			if target:FindFirstChildOfClass("Humanoid") then
				DebrisService:AddItem(StunEffect,duration)
				animationTrack:Stop()
				humanoid.WalkSpeed = target.Config.NormalSpeed.Value 
			end
		end)
	end
end
function tower.Melt(tower, target, duration)
	local MeltDamage = tower.Config:FindFirstChild("MeltDamage").Value or 1
	local MeltEffect = Particles:FindFirstChild("MeltEffect"):Clone()
	local humanoid = target:FindFirstChild("Humanoid")

	if humanoid then
		if not target.Torso:FindFirstChild("MeltEffect")or target.HumanoidRootPart:FindFirstChild("MeltEffect") then
			MeltEffect.Parent = target.Torso or target.HumanoidRootPart
			MeltEffect.Color = ColorSequence.new(target.Torso.Color) or ColorSequence.new(1, 1, 1)
			for i, v in pairs(target:GetChildren()) do
				if v:IsA("Script") then
					v.Enabled = false
				end
			end
		end
		spawn(function()
			task.wait(duration)
			for i = 1,duration do
				humanoid:TakeDamage(MeltDamage)
			end
			DebrisService:AddItem(MeltEffect,duration)
			if target:FindFirstChildOfClass("Humanoid") then
				for i, v in pairs(target:GetChildren()) do
					if v:IsA("Script") then
						v.Enabled = true
					end
				end
			end
		end)
	end
end
function tower.FaceTarget(newTower,target,duration)
	pcall(function()
		local targetVector = Vector3.new(target.PrimaryPart.Position.X,newTower.PrimaryPart.Position.Y,target.PrimaryPart.Position.Z)
		local targetCFrame = CFrame.new(newTower.PrimaryPart.Position, targetVector)
		local tweenInfo = TweenInfo.new(duration, Enum.EasingStyle.Linear, Enum.EasingDirection.Out, 0, false, 0)
		local faceTargetTween = TweenService:Create(newTower.PrimaryPart, tweenInfo, {CFrame = targetCFrame})
		faceTargetTween:Play()
	end)
end

function tower.Attack(newTower, player)

	local config = newTower:WaitForChild("Config")
	local target = tower.FindTarget(newTower, config.Range.Value, config.TargetMode.Value)

	if config:FindFirstChild("Charging") and not config:FindFirstChild("Charged") then
		if target and config:FindFirstChild("Charging").Value < config:FindFirstChild("ChargeTime").Value then 
			if config:FindFirstChild("Charging").Value < config:FindFirstChild("ChargeTime").Value then
				local chargeSound = newTower:FindFirstChild("Head"):FindFirstChild("ChargeSound")
				animateTowerChargeEvent:FireAllClients(newTower, "Charge", config:FindFirstChild("ChargeTime").Value or 1)
				if chargeSound then
					chargeSound.PlaybackSpeed = 1 / config.ChargeTime.Value  -- Adjust playback speed based on ChargeTime
					chargeSound:Play()
				end
				repeat

					tower.FaceTarget(newTower, target, 0.06) 
					task.wait(0.1)
					config:FindFirstChild("Charging").Value += 0.1
				until

				config:FindFirstChild("Charging").Value >= config:FindFirstChild("ChargeTime").Value or not target

				-- sound effects here
			end
		elseif not target and config:FindFirstChild("Charging").Value >= config:FindFirstChild("ChargeTime").Value then 
			repeat
				task.wait(0.1)
				config:FindFirstChild("Charging").Value = 0

			until 
			config:FindFirstChild("Charging").Value <= 0 or target or not config.Debuffs:FindFirstChild("Stunned")
			if config:FindFirstChild("Charging").Value < 0 then
				config:FindFirstChild("Charing").Value = 0

			end
		end
	end

	if target and target:FindFirstChild("Humanoid") and target.Humanoid.Health > 0 then
		tower.FaceTarget(newTower,target,0.07)
		if newTower.Config:FindFirstChild("Debuffs") and newTower.Config:WaitForChild("Debuffs"):FindFirstChild("Stunned") then
			local StunnedParticle = ReplicatedStorage.Particles.StunParticle:Clone()
			StunnedParticle.Parent = newTower:WaitForChild("Head")
			StunnedParticle.Enabled = true
			wait(newTower.Config.Debuffs.Stunned.Value)
			StunnedParticle:Destroy()
			newTower.Config.Debuffs:ClearAllChildren()
		else
			local targetCFrame = CFrame.lookAt(newTower.HumanoidRootPart.Position, target.HumanoidRootPart.Position)
			if newTower.Head:FindFirstChild("Shoot") then
				newTower.Head.Shoot:Play()
			end

			animateTowerEvent:FireAllClients(newTower, "Attack", target)
			

			if not target.Config:FindFirstChild("DefensePercent") then
				if not target.Config:FindFirstChild("NoMoney") or target.Config.NoMoney.Value == false then
				if target.Humanoid.Health < config.Damage.Value then
					SendCash(player, target.Humanoid.Health)
					config.DamageDealt.Value += target.Humanoid.Health
				else
					SendCash(player, config.Damage.Value)
					config.DamageDealt.Value += config.Damage.Value
				end
				end
				target.Humanoid:TakeDamage(config.Damage.Value)

			else
				if not target.Config:FindFirstChild("NoMoney") or target.Config.NoMoney.Value == false then
				if target.Humanoid.Health < math.round(config.Damage.Value - (config.Damage.Value * (target.Config.DefensePercent.Value / 100))) then
					
					SendCash(player, target.Humanoid.Health)
					config.DamageDealt.Value += target.Humanoid.Health
				else
					SendCash(player, math.round(config.Damage.Value - (config.Damage.Value * (target.Config.DefensePercent.Value / 100))))
					config.DamageDealt.Value += math.round(config.Damage.Value - (config.Damage.Value * (target.Config.DefensePercent.Value / 100)))
				end
				end
				target.Humanoid:TakeDamage(math.round(config.Damage.Value - (config.Damage.Value * (target.Config.DefensePercent.Value / 100))))

			end
			if not config:FindFirstChild("StunTime") then
			elseif config:FindFirstChild("StunTime") then
				if target.Config.NoStun.Value == false then
					if target.Config.IsBoss.Value == false then
						tower.Stun(newTower,target,config.StunTime.Value)
					end
				end
			end
			if not config:FindFirstChild("AfterMelt") then
			elseif config:FindFirstChild("AfterMelt") then
				if target.Config.NoStun.Value == false then
					if target.Config.IsBoss.Value == false then
						tower.Melt(newTower,target,config.AfterMelt.Value or 3)
					end
				end
			end



			if target.Humanoid.Health <= 0 then
				player.Kills.Value += 1
			end

			task.wait(config.Cooldown.Value)
		end
	end
	task.wait()
	if newTower and newTower.Parent then
		task.spawn(function()
			tower.Attack(newTower, player)
		end)
	end
end


function tower.ChangeMode(player, model)
	if model and model:FindFirstChild("Config") then
		local targetMode = model.Config.TargetMode
		local modes = {"First", "Last", "Strongest", "Weakest", "Nearest","Random"}
		local modeIndex = table.find(modes, targetMode.Value)

		if modeIndex < #modes then
			targetMode.Value = modes[modeIndex + 1]
		else
			targetMode.Value = modes[1]
		end

		return true
	else
		warn("Unable to change tower mode")
		return false
	end
end
changeModeFunction.OnServerInvoke = tower.ChangeMode

function tower.Sell(player, model)
	if model and model:FindFirstChild("Config") then
		if model.Config.Owner.Value == player.Name then
			local towerName = model.Config.OriginalTower.Value
			player.PlacedTowers.Value -= 1

			-- Decrease the count for the specific tower type
			if IndividualTowersOnLimit[player.Name][towerName] then
				IndividualTowersOnLimit[player.Name][towerName] -= 1
			end

			player.Gold.Value += model.Config.Spent.Value / 3
			model:Destroy()
			return true
		end
	end

	warn("Unable to sell this tower")
	return false
end

sellTowerFunction.OnServerInvoke = tower.Sell

function tower.Spawn(player, name, cframe, previous)
	-- Ensure 'name' is a string
	if typeof(name) ~= "string" then
		warn("Expected 'name' to be a string, but received: " .. typeof(name))
		return false
	end

	local allowedToSpawn = tower.CheckSpawn(player, name, previous)

	if allowedToSpawn then
		local newTower
		local oldMode = nil
		local oldDamageDealt = 0
		local oldSpent = 0
		local oldOriginalTower = nil

		if previous then
			-- Collect existing values from the previous tower
			oldMode = previous.Config.TargetMode.Value 
			oldDamageDealt = previous.Config.DamageDealt.Value
			oldSpent = previous.Config.Spent.Value
			oldOriginalTower = previous.Config.OriginalTower.Value  -- Preserve original tower name
			if IndividualTowersOnLimit[player.Name][oldOriginalTower] then
				IndividualTowersOnLimit[player.Name][oldOriginalTower] -= 1
			end
			previous:Destroy()  -- Destroy previous tower if upgrading
			newTower = ReplicatedStorage.Towers.Upgrades[name]:Clone()  -- Clone upgraded tower
		else
			newTower = ReplicatedStorage.Towers[name]:Clone()  -- Clone original tower
			player.PlacedTowers.Value += 1  -- Increment the player's tower count
		end

		-- Set up the new tower's configuration
		local ownerValue = Instance.new("StringValue")
		ownerValue.Name = "Owner"
		ownerValue.Value = player.Name
		ownerValue.Parent = newTower.Config

		local targetMode = Instance.new("StringValue")
		targetMode.Name = "TargetMode"
		targetMode.Value = oldMode or "First"  -- Use existing mode or default to "First"
		targetMode.Parent = newTower.Config

		local DamageDealt = Instance.new("NumberValue")
		DamageDealt.Name = "DamageDealt"
		DamageDealt.Value = oldDamageDealt or 0
		DamageDealt.Parent = newTower.Config

		local Spent = Instance.new("NumberValue")
		Spent.Name = "Spent"
		Spent.Value = oldSpent + (newTower.Config.Price.Value or 0)  -- Calculate total spent
		Spent.Parent = newTower.Config

		local OriginalTower = newTower.Config:FindFirstChild("OriginalTower")
		if not OriginalTower then
			OriginalTower = Instance.new("StringValue")
			OriginalTower.Name = "OriginalTower"
			OriginalTower.Value = oldOriginalTower or name  -- Set to original if upgrading or current name
			OriginalTower.Parent = newTower.Config
		else
			OriginalTower.Value = oldOriginalTower or name  -- Ensure correct original name
		end

		-- Set MaxTowers based on the original tower
		local originalTowerConfig = previous and ReplicatedStorage.Towers[oldOriginalTower] or ReplicatedStorage.Towers[name]
		local IndPlacementLimit = originalTowerConfig:FindFirstChild("MaxTowers")

		if not IndPlacementLimit then
			IndPlacementLimit = Instance.new("IntValue")
			IndPlacementLimit.Name = "MaxTowers"
			IndPlacementLimit.Value = 200
			IndPlacementLimit.Parent = newTower.Config
		else
			local maxTowersValue = IndPlacementLimit.Value
			local maxTowersInstance = Instance.new("IntValue")
			maxTowersInstance.Name = "MaxTowers"
			maxTowersInstance.Value = maxTowersValue
			maxTowersInstance.Parent = newTower.Config
		end

		-- Position and parent the new tower
		newTower:SetPrimaryPartCFrame(cframe)
		newTower.Parent = workspace.Towers
		tower.Optimize(newTower)

		-- Set collision group
		for _, object in ipairs(newTower:GetDescendants()) do
			if object:IsA("BasePart") then
				object.CollisionGroup = "Tower"
			end
		end

		-- Create boundary part for tower
		local height = (newTower.PrimaryPart.Size.Y / 2) + newTower.PrimaryPart.Size.Y 
		local offset = Vector3.new(0, -height, 0)
		local boundaryPart = Instance.new("Part")
		boundaryPart.Name = "Boundary"
		boundaryPart.Shape = Enum.PartType.Block
		boundaryPart.Transparency = 1
		boundaryPart.BrickColor = BrickColor.new("Persimmon")
		boundaryPart.Size = Vector3.new(newTower.Config.BoundarySize.Value, 0.3, newTower.Config.BoundarySize.Value)
		boundaryPart.Material = Enum.Material.Neon
		boundaryPart.TopSurface = Enum.SurfaceType.Smooth
		boundaryPart.BottomSurface = Enum.SurfaceType.Smooth
		boundaryPart.CanCollide = false
		boundaryPart.CanQuery = true
		boundaryPart.Parent = newTower
		boundaryPart.Anchored = true
		boundaryPart.Position = newTower.PrimaryPart.Position + offset
		boundaryPart.CFrame = newTower.PrimaryPart.CFrame * CFrame.new(offset)

		-- Deduct the cost from the player's gold
		player.Gold.Value -= newTower.Config.Price.Value 

		-- Increment the count for the specific tower type
		local towerName = previous and oldOriginalTower or name
		IndividualTowersOnLimit[player.Name][towerName] = (IndividualTowersOnLimit[player.Name][towerName] or 0) + 1

		-- Start attacking if necessary
		task.delay(newTower.Config.Cooldown.Value, function()
		coroutine.wrap(tower.Attack)(newTower, player)
		end)
		return newTower
	else
		warn("Requested tower does not exist:", name)
		return false
	end
end


spawnTowerFunction.OnServerInvoke = tower.Spawn


local function initializePlayerData(player)
	IndividualTowersOnLimit[player.Name] = {}
	for _, tower in ipairs(ReplicatedStorage.Towers:GetChildren()) do
		IndividualTowersOnLimit[player.Name][tower.Name] = 0
	end
end

-- Hook player initialization
Players.PlayerAdded:Connect(initializePlayerData)

function tower.CheckSpawn(player, name, previous)
	if type(name) ~= "string" then
		warn("Expected 'name' to be a string, but received: " .. type(name))
		return false
	end

	local towerExists = ReplicatedStorage.Towers:FindFirstChild(name, true)

	if towerExists then
		-- Check if the player can afford the tower
		if towerExists.Config.Price and towerExists.Config.Price:IsA("IntValue") then
			if towerExists.Config.Price.Value <= player.Gold.Value then
				-- Check individual tower limits
				local individualMaxTowers = towerExists.Config:FindFirstChild("MaxTowers")
				local maxAllowed = individualMaxTowers and individualMaxTowers.Value or 999
				local currentTowers = IndividualTowersOnLimit[player.Name][name] or 0

				-- Debug prints for checking values
				print("Max allowed for tower " .. name .. ": " .. maxAllowed)
				print("Current towers placed by player " .. player.Name .. ": " .. currentTowers)

				if previous or currentTowers < maxAllowed then
					if player.Alive.Value == true then
						if previous or player.PlacedTowers.Value < maxTowers then
							print()
						return true 
						else
							messageEvent:FireClient(player, "Player has reached max limit of towers.")
						end
					else
						messageEvent:FireClient(player, "Can't place tower while dead.")
					end
				else
					messageEvent:FireClient(player, "Player has reached max limit for this tower.")
					warn("Player has reached max limit for this tower: " .. player.Name)
				end
			else
				messageEvent:FireClient(player, "Player cannot afford.")
				warn("Player cannot afford: " .. player.Name)
			end
		else
			warn("Tower price is not a valid IntValue.")
		end
	else
		warn("Requested tower does not exist: " .. name)
	end
	return false
end

requestTowerFunction.OnServerInvoke = tower.CheckSpawn

return tower